#!/usr/bin/env node

/**
 * Debug script to test the purchase flow step by step
 */

import { ethers } from 'ethers';
import fetch from 'node-fetch';

const provider = new ethers.JsonRpcProvider('http://localhost:8545');
const gameServerUrl = 'http://localhost:3001';

const buyer = {
    address: '******************************************',
    wallet: new ethers.Wallet('0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d', provider)
};

const creator = {
    address: '******************************************',
    wallet: new ethers.Wallet('0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a', provider)
};

const HOT_WALLET_ADDRESS = '******************************************';

async function debugPurchaseFlow() {
    console.log('🔍 Debugging Purchase Flow');
    console.log('==========================');
    
    try {
        // Step 1: Create environment
        console.log('Step 1: Creating environment...');
        const environmentData = {
            name: `Debug Test Environment ${Date.now()}`,
            description: 'A test environment for debugging',
            gameplayModifiers: {
                environmentType: 'mystical',
                difficulty: 'normal'
            }
        };

        const createResponse = await fetch(`${gameServerUrl}/api/environments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                environmentData,
                creatorUserId: creator.address
            })
        });

        if (!createResponse.ok) {
            const errorText = await createResponse.text();
            throw new Error(`Environment creation failed: ${createResponse.status} - ${errorText}`);
        }

        const createResult = await createResponse.json();
        console.log('✅ Environment created:', createResult);
        
        const environment = createResult.environment;
        if (!environment || !environment.id) {
            throw new Error('Environment creation returned invalid data');
        }

        // Step 2: Check initial balances
        console.log('\nStep 2: Checking initial balances...');
        const initialBuyerBalance = await provider.getBalance(buyer.address);
        const initialCreatorBalance = await provider.getBalance(creator.address);
        const initialTreasuryBalance = await provider.getBalance(HOT_WALLET_ADDRESS);
        
        console.log(`Buyer balance: ${ethers.formatEther(initialBuyerBalance)} ETH`);
        console.log(`Creator balance: ${ethers.formatEther(initialCreatorBalance)} ETH`);
        console.log(`Treasury balance: ${ethers.formatEther(initialTreasuryBalance)} ETH`);

        // Step 3: Send ETH transaction
        console.log('\nStep 3: Sending ETH transaction...');
        const purchaseCost = 0.01;
        
        const tx = await buyer.wallet.sendTransaction({
            to: HOT_WALLET_ADDRESS,
            value: ethers.parseEther(purchaseCost.toString()),
            gasLimit: 21000
        });

        console.log(`Transaction sent: ${tx.hash}`);
        
        const receipt = await tx.wait();
        console.log(`✅ Transaction confirmed in block ${receipt.blockNumber}`);

        // Step 4: Record purchase on server
        console.log('\nStep 4: Recording purchase on server...');
        const purchaseResponse = await fetch(`${gameServerUrl}/api/environments/${environment.id}/purchase`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                purchaserUserId: buyer.address,
                cost: purchaseCost
            })
        });

        if (!purchaseResponse.ok) {
            const errorText = await purchaseResponse.text();
            console.error(`❌ Purchase recording failed: ${purchaseResponse.status} - ${errorText}`);
            return;
        }

        const purchaseData = await purchaseResponse.json();
        console.log('✅ Purchase recorded:', purchaseData);

        // Step 5: Distribute creator reward
        console.log('\nStep 5: Distributing creator reward...');
        const rewardAmount = purchaseCost * 0.5; // 50% to creator
        
        const rewardResponse = await fetch(`${gameServerUrl}/api/rewards/distribute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer secure-token-for-development'
            },
            body: JSON.stringify({
                creatorUserId: creator.address,
                amount: rewardAmount.toString(),
                reason: `creator_reward_${environment.id}`
            })
        });

        if (!rewardResponse.ok) {
            const errorText = await rewardResponse.text();
            console.error(`❌ Creator reward distribution failed: ${rewardResponse.status} - ${errorText}`);
            return;
        }

        const rewardResult = await rewardResponse.json();
        console.log('✅ Creator reward distributed:', rewardResult);

        // Step 6: Check final balances
        console.log('\nStep 6: Checking final balances...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for transactions to settle
        
        const finalBuyerBalance = await provider.getBalance(buyer.address);
        const finalCreatorBalance = await provider.getBalance(creator.address);
        const finalTreasuryBalance = await provider.getBalance(HOT_WALLET_ADDRESS);
        
        console.log(`Final buyer balance: ${ethers.formatEther(finalBuyerBalance)} ETH`);
        console.log(`Final creator balance: ${ethers.formatEther(finalCreatorBalance)} ETH`);
        console.log(`Final treasury balance: ${ethers.formatEther(finalTreasuryBalance)} ETH`);
        
        // Calculate changes
        const buyerPaid = initialBuyerBalance - finalBuyerBalance;
        const creatorReceived = finalCreatorBalance - initialCreatorBalance;
        const treasuryChange = finalTreasuryBalance - initialTreasuryBalance;
        
        console.log('\n📊 Balance Changes:');
        console.log(`Buyer paid: ${ethers.formatEther(buyerPaid)} ETH`);
        console.log(`Creator received: ${ethers.formatEther(creatorReceived)} ETH`);
        console.log(`Treasury change: ${ethers.formatEther(treasuryChange)} ETH`);
        
        console.log('\n✅ Purchase flow completed successfully!');
        
    } catch (error) {
        console.error('❌ Error in purchase flow:', error);
        console.error('Error details:', error.message);
    }
}

debugPurchaseFlow();
