# ON-CHAIN Treasury Drain Protection Tests

This directory contains **REAL ON-CHAIN TESTS** that simulate actual users making genuine ETH transactions through your game server to test treasury drain protection.

## 🎯 What These Tests Do

### REAL User Simulation
- Uses **ACTUAL wallets** from your Hardhat local node
- Makes **REAL ETH transactions** to your hot wallet
- Calls **YOUR actual game server APIs**
- Tests **REAL creator rewards** for Mystical Environment purchases
- Monitors **ACTUAL treasury balance changes** ON-CHAIN

### Test Scenarios

#### 1. Grinder User Test
- Simulates a user who earns maximum tokens through gameplay
- Assumes grinder earns **100%** of maximum possible (not 85%)
- Assumes grinder spends **NOTHING** (not 5%)
- Verifies treasury is not drained by pure earning behavior

#### 2. Creator Environment Purchase Test
- Creates a **REAL Mystical Environment** through your server
- User makes **ACTUAL ETH transaction** to purchase environment
- Tests **REAL creator reward distribution** (50% to creator)
- Verifies treasury receives remaining funds
- Confirms **ON-CHAIN balance changes** for all parties

#### 3. Multi-User Attack Test
- Coordinates **REAL attack** from multiple wallets simultaneously
- Tests rapid earning/withdrawal patterns
- Verifies treasury protection mechanisms work
- Ensures treasury is not significantly drained

## 🚀 How to Run the Tests

### Prerequisites

1. **Start Hardhat Local Node**
   ```bash
   npx hardhat node
   ```
   This gives you 20 test accounts with 10,000 ETH each.

2. **Start Your Game Server**
   ```bash
   node server/index.js
   ```
   Make sure it's running on localhost:3001.

3. **Install Dependencies**
   ```bash
   npm install
   ```

### Run the Tests

#### Option 1: Use the Test Runner (Recommended)
```bash
node test/run-onchain-tests.js
```

#### Option 2: Run Jest Directly
```bash
npm test test/tokenomics/OnChainTreasuryTest.js
```

## 🔧 Test Configuration

### Hardhat Accounts Used
The tests use these **ACTUAL** accounts from your Hardhat node:

- **Account #0**: `******************************************` (Grinder User)
- **Account #1**: `******************************************` (Buyer)
- **Account #2**: `******************************************` (Creator)
- **Account #3**: `******************************************` (Attacker 1)
- **Account #4**: `******************************************` (Attacker 2)

### Hot Wallet
- **Address**: `******************************************`
- **Private Key**: `0xde9be858da4a475276426320d5e9262ecfc3ba460bfac56360bfa6c4c28b4ee0`

## 📊 What the Tests Verify

### Treasury Protection
- ✅ Treasury balance doesn't get drained by grinder users
- ✅ Creator rewards are properly distributed ON-CHAIN
- ✅ Treasury receives appropriate percentage of purchases
- ✅ Multi-user attacks are contained
- ✅ Rate limiting and protection mechanisms work

### Creator Rewards
- ✅ 50% of Mystical Environment purchases go to creator
- ✅ Creator receives **ACTUAL ETH** in their wallet
- ✅ Treasury receives remaining funds
- ✅ All transactions are **REAL and ON-CHAIN**

### Attack Resistance
- ✅ Rapid earning attempts are rate-limited
- ✅ Coordinated multi-user attacks don't drain treasury
- ✅ Protection mechanisms trigger correctly
- ✅ Treasury maintains healthy balance

## 🔍 Test Output

The tests provide detailed logging:

```
🏦 ACTUAL Treasury Balance: 10000.0 ETH
✅ YOUR Game server is running and accessible

🎮 Testing REAL Grinder User: 0xf39fd6e5...
💰 User f39fd6e5 earned 0.001 ETH: gameplay_session_1
💰 Grinder completed 10 earning sessions
🏦 Treasury balance change: 0.0 ETH

🎨 Testing REAL Creator Environment Purchase ON-CHAIN
👤 Buyer: 0x70997970...
🎭 Creator: 0x3c44cddd...
🌍 Created environment: env_1_1234567890
💳 Making REAL ETH transaction: 0.01 ETH to 0xdd2fd458...
✅ REAL transaction confirmed in block 123: 0xabc123...
🎁 Creator reward distribution initiated: 0.005 ETH
💸 Buyer paid: 0.010039 ETH
🎁 Creator received: 0.005 ETH
🏦 Treasury increase: 0.004 ETH
```

## ⚠️ Important Notes

### This is NOT Simulation
- These tests make **REAL ETH transactions**
- They use **YOUR actual game server**
- They test **REAL on-chain behavior**
- Treasury changes are **ACTUAL and PERMANENT** (on test network)

### Test Environment
- Uses Hardhat local test network only
- All ETH is test ETH (no real value)
- Hot wallet uses test private key
- Safe to run repeatedly

### What Gets Tested
- ✅ **REAL user behavior** through game functions
- ✅ **ACTUAL on-chain transactions**
- ✅ **YOUR game server APIs**
- ✅ **REAL creator reward distribution**
- ❌ NOT simulated game logic
- ❌ NOT fake transactions
- ❌ NOT mocked APIs

## 🎯 Success Criteria

The tests pass when:
1. Treasury is not significantly drained by any user behavior
2. Creator rewards are properly distributed ON-CHAIN
3. Attack attempts are successfully blocked
4. All transactions are real and confirmed on-chain
5. Treasury maintains healthy balance throughout all scenarios

This ensures your tokenomics are sustainable and your treasury is protected against drain attacks in the real world.
