
import { TokenomicsTestFramework } from './TokenomicsTestFramework.js';
import fs from 'fs';
import path from 'path';

/**
 * Tokenomics Test Runner - Orchestrates comprehensive tokenomics testing
 */
export class TokenomicsTestRunner {
    constructor() {
        this.framework = new TokenomicsTestFramework();
        this.testSuites = [];
        this.currentSuite = null;
    }

    /**
     * Initialize the test runner
     */
    async initialize() {
        console.log('🚀 Initializing Tokenomics Test Runner...');
        
        try {
            await this.framework.initialize();
            console.log('✅ Tokenomics Test Runner initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize test runner:', error);
            throw error;
        }
    }

    /**
     * Run all test suites
     */
    async runAllTests() {
        console.log('🧪 Starting comprehensive tokenomics testing...');
        
        const startTime = Date.now();
        
        try {
            // Define test suites
            this.testSuites = [
                this.createSingleUserEconomicsSuite(),
                this.createMultiUserEconomicsSuite(),
                this.createAttackScenariosSuite(),
                this.createCreatorRewardSuite(),
                this.createSustainedOperationSuite()
            ];

            // Run each test suite
            for (const suite of this.testSuites) {
                console.log(`\n📋 Running test suite: ${suite.name}`);
                this.currentSuite = suite;
                
                try {
                    await this.runTestSuite(suite);
                    console.log(`✅ Test suite completed: ${suite.name}`);
                } catch (error) {
                    console.error(`❌ Test suite failed: ${suite.name}`, error);
                    suite.success = false;
                    suite.errors.push(error.message);
                }
            }

            // Generate final report
            const report = this.generateFinalReport(startTime);
            
            // Save report to file
            await this.saveReport(report);
            
            console.log('\n🎉 All tokenomics tests completed!');
            console.log(`📊 Final Report: ${report.summary.totalTests} tests, ${report.summary.passedTests} passed, ${report.summary.failedTests} failed`);
            
            return report;

        } catch (error) {
            console.error('❌ Tokenomics testing failed:', error);
            throw error;
        }
    }

    /**
     * Create single user economics test suite
     */
    createSingleUserEconomicsSuite() {
        return {
            name: 'Single User Economics',
            description: 'Test individual user behavior patterns and their economic impact',
            tests: [
                {
                    name: 'Grinder User Test',
                    description: 'Test grinder behavior: maximum earning, minimal spending',
                    config: {
                        userCount: 1,
                        behaviorTypes: ['grinder'],
                        sessionConfig: { numLevels: 15 },
                        duration: 30000 // 30 seconds
                    }
                },
                {
                    name: 'Casual Player Test',
                    description: 'Test casual player behavior: moderate play and spending',
                    config: {
                        userCount: 1,
                        behaviorTypes: ['casual'],
                        sessionConfig: { numLevels: 8 },
                        duration: 30000
                    }
                },
                {
                    name: 'Whale User Test',
                    description: 'Test whale behavior: heavy spending, minimal grinding',
                    config: {
                        userCount: 1,
                        behaviorTypes: ['whale'],
                        sessionConfig: { numLevels: 3 },
                        duration: 30000
                    }
                },
                {
                    name: 'Creator User Test',
                    description: 'Test creator behavior: content creation and rewards',
                    config: {
                        userCount: 1,
                        behaviorTypes: ['creator'],
                        sessionConfig: { numLevels: 5 },
                        duration: 30000
                    }
                }
            ]
        };
    }

    /**
     * Create multi-user economics test suite
     */
    createMultiUserEconomicsSuite() {
        return {
            name: 'Multi-User Economics',
            description: 'Test economic balance with multiple users of different types',
            tests: [
                {
                    name: 'Balanced Ecosystem Test',
                    description: 'Test with balanced mix of user types',
                    config: {
                        userCount: 10,
                        behaviorTypes: ['grinder', 'grinder', 'grinder', 'casual', 'casual', 'whale', 'whale', 'creator', 'creator', 'casual'],
                        sessionConfig: { numLevels: 10 },
                        duration: 60000 // 1 minute
                    }
                },
                {
                    name: 'Grinder Heavy Test',
                    description: 'Test ecosystem with majority grinders (potential drain scenario)',
                    config: {
                        userCount: 10,
                        behaviorTypes: ['grinder', 'grinder', 'grinder', 'grinder', 'grinder', 'grinder', 'grinder', 'casual', 'casual', 'whale'],
                        sessionConfig: { numLevels: 12 },
                        duration: 60000
                    }
                },
                {
                    name: 'Whale Heavy Test',
                    description: 'Test ecosystem with majority whales (revenue positive)',
                    config: {
                        userCount: 8,
                        behaviorTypes: ['whale', 'whale', 'whale', 'whale', 'whale', 'casual', 'casual', 'creator'],
                        sessionConfig: { numLevels: 5 },
                        duration: 60000
                    }
                }
            ]
        };
    }

    /**
     * Create attack scenarios test suite
     */
    createAttackScenariosSuite() {
        return {
            name: 'Attack Scenarios',
            description: 'Test treasury protection against various attack vectors',
            tests: [
                {
                    name: 'Sequential Grinding Attack',
                    description: 'Multiple wallets grinding sequentially to test earning limits',
                    config: {
                        userCount: 5,
                        behaviorTypes: ['grinder', 'grinder', 'grinder', 'grinder', 'grinder'],
                        sessionConfig: { numLevels: 20 },
                        duration: 120000, // 2 minutes
                        attackPattern: 'sequential'
                    }
                },
                {
                    name: 'Rapid Purchase Attack',
                    description: 'Rapid purchase requests to test rate limiting',
                    config: {
                        userCount: 3,
                        behaviorTypes: ['whale', 'whale', 'whale'],
                        sessionConfig: { numLevels: 2 },
                        duration: 60000,
                        attackPattern: 'rapid_purchase'
                    }
                },
                {
                    name: 'Multi-Account Sybil Attack',
                    description: 'Same user using multiple wallets simultaneously',
                    config: {
                        userCount: 10,
                        behaviorTypes: Array(10).fill('grinder'),
                        sessionConfig: { numLevels: 15 },
                        duration: 90000, // 1.5 minutes
                        attackPattern: 'sybil'
                    }
                },
                {
                    name: 'Creator Reward Abuse',
                    description: 'Test creator reward system against abuse',
                    config: {
                        userCount: 6,
                        behaviorTypes: ['creator', 'creator', 'whale', 'whale', 'casual', 'casual'],
                        sessionConfig: { numLevels: 5 },
                        duration: 60000,
                        attackPattern: 'creator_abuse'
                    }
                }
            ]
        };
    }

    /**
     * Create creator reward test suite
     */
    createCreatorRewardSuite() {
        return {
            name: 'Creator Reward Testing',
            description: 'Test creator reward distribution and accuracy',
            tests: [
                {
                    name: 'Single Creator Reward Test',
                    description: 'Test creator reward for single environment purchase',
                    config: {
                        userCount: 2,
                        behaviorTypes: ['creator', 'whale'],
                        sessionConfig: { numLevels: 3 },
                        duration: 30000,
                        creatorTest: true,
                        creatorScenarios: ['single_purchase']
                    }
                },
                {
                    name: 'Multiple Creator Rewards',
                    description: 'Test multiple creators receiving rewards',
                    config: {
                        userCount: 5,
                        behaviorTypes: ['creator', 'creator', 'whale', 'whale', 'casual'],
                        sessionConfig: { numLevels: 4 },
                        duration: 45000,
                        creatorTest: true,
                        creatorScenarios: ['multiple_creators']
                    }
                },
                {
                    name: 'Creator Reward Accuracy',
                    description: 'Verify creator reward amounts are calculated correctly',
                    config: {
                        userCount: 4,
                        behaviorTypes: ['creator', 'whale', 'whale', 'whale'],
                        sessionConfig: { numLevels: 2 },
                        duration: 30000,
                        creatorTest: true,
                        creatorScenarios: ['reward_accuracy']
                    }
                }
            ]
        };
    }

    /**
     * Create sustained operation test suite
     */
    createSustainedOperationSuite() {
        return {
            name: 'Sustained Operation',
            description: 'Test long-term economic sustainability',
            tests: [
                {
                    name: 'Extended Mixed User Test',
                    description: 'Run extended test with mixed user types',
                    config: {
                        userCount: 15,
                        behaviorTypes: [
                            'grinder', 'grinder', 'grinder', 'grinder', 'grinder',
                            'casual', 'casual', 'casual', 'casual',
                            'whale', 'whale', 'whale',
                            'creator', 'creator', 'casual'
                        ],
                        sessionConfig: { numLevels: 25 },
                        duration: 300000, // 5 minutes
                        extended: true
                    }
                },
                {
                    name: 'Revenue Sustainability Test',
                    description: 'Test if system remains revenue positive over time',
                    config: {
                        userCount: 20,
                        behaviorTypes: Array(20).fill(null).map((_, i) => {
                            if (i < 8) return 'grinder';
                            if (i < 14) return 'casual';
                            if (i < 18) return 'whale';
                            return 'creator';
                        }),
                        sessionConfig: { numLevels: 20 },
                        duration: 600000, // 10 minutes
                        extended: true,
                        sustainabilityTarget: 0.1 // Target 10% profit margin
                    }
                }
            ]
        };
    }

    /**
     * Run individual test suite
     */
    async runTestSuite(suite) {
        console.log(`🎯 Running ${suite.tests.length} tests in suite: ${suite.name}`);
        
        suite.startTime = Date.now();
        suite.results = [];
        suite.success = true;
        suite.errors = [];

        for (const test of suite.tests) {
            console.log(`\n🔬 Running test: ${test.name}`);
            
            try {
                const result = await this.runIndividualTest(test);
                suite.results.push(result);
                
                if (!result.success) {
                    suite.success = false;
                    suite.errors.push(`${test.name}: ${result.error}`);
                }
                
                console.log(`✅ Test completed: ${test.name} (${result.duration}ms)`);
                
            } catch (error) {
                console.error(`❌ Test failed: ${test.name}`, error);
                suite.success = false;
                suite.errors.push(`${test.name}: ${error.message}`);
            }
        }

        suite.endTime = Date.now();
        suite.duration = suite.endTime - suite.startTime;
        
        console.log(`📊 Suite ${suite.name} completed: ${suite.results.filter(r => r.success).length}/${suite.results.length} tests passed`);
    }

    /**
     * Run individual test
     */
    async runIndividualTest(test) {
        const testResult = {
            name: test.name,
            description: test.description,
            startTime: Date.now(),
            success: true,
            metrics: {},
            errors: []
        };

        try {
            // Run the test scenario
            const scenario = await this.framework.runTestScenario({
                name: test.name,
                ...test.config
            });

            // Analyze results
            testResult.scenario = scenario;
            testResult.success = scenario.success;
            testResult.errors = scenario.errors;

            // Check for treasury drain
            const treasuryChange = scenario.metrics.treasuryChange;
            if (treasuryChange < -0.1) { // Lost more than 0.1 ETH
                testResult.warnings = [`Significant treasury drain detected: ${treasuryChange.toFixed(4)} ETH`];
            }

            // Check economic balance
            const netFlow = scenario.metrics.netFlow;
            if (netFlow < 0 && test.config.userCount > 1) {
                testResult.warnings = [`Negative net flow: ${netFlow.toFixed(4)} ETH`];
            }

            // Validate creator rewards if applicable
            if (test.config.creatorTest) {
                const creatorRewardValidation = this.validateCreatorRewards(scenario);
                if (!creatorRewardValidation.valid) {
                    testResult.success = false;
                    testResult.errors.push(`Creator reward validation failed: ${creatorRewardValidation.error}`);
                }
            }

            // Check attack protection if applicable
            if (test.config.attackPattern) {
                const protectionValidation = this.validateAttackProtection(scenario, test.config.attackPattern);
                if (!protectionValidation.valid) {
                    testResult.success = false;
                    testResult.errors.push(`Attack protection failed: ${protectionValidation.error}`);
                }
            }

        } catch (error) {
            console.error(`❌ Test execution failed: ${test.name}`, error);
            testResult.success = false;
            testResult.errors.push(error.message);
        } finally {
            testResult.endTime = Date.now();
            testResult.duration = testResult.endTime - testResult.startTime;
        }

        return testResult;
    }

    /**
     * Validate creator rewards
     */
    validateCreatorRewards(scenario) {
        const creatorPurchases = scenario.sessions.flatMap(session => 
            session.purchasesMade.filter(purchase => purchase.itemType === 'creator_environment')
        );

        if (creatorPurchases.length === 0) {
            return { valid: true, message: 'No creator purchases in this test' };
        }

        // Check that creator rewards were distributed
        const totalCreatorRewards = creatorPurchases.reduce((sum, purchase) => 
            sum + (purchase.creatorReward || 0), 0
        );

        if (totalCreatorRewards === 0) {
            return { valid: false, error: 'Creator purchases made but no rewards distributed' };
        }

        // Validate reward amounts (should be percentage of purchase)
        for (const purchase of creatorPurchases) {
            if (!purchase.creatorReward || purchase.creatorReward <= 0) {
                return { valid: false, error: `Missing creator reward for purchase of ${purchase.cost} ETH` };
            }
            
            // Creator reward should be reasonable percentage (5-20% of purchase)
            const rewardPercentage = (purchase.creatorReward / purchase.cost) * 100;
            if (rewardPercentage < 5 || rewardPercentage > 20) {
                return { valid: false, error: `Creator reward percentage unreasonable: ${rewardPercentage.toFixed(2)}%` };
            }
        }

        return { valid: true, message: 'Creator rewards validated successfully' };
    }

    /**
     * Validate attack protection
     */
    validateAttackProtection(scenario, attackPattern) {
        const metrics = scenario.metrics;
        
        switch (attackPattern) {
            case 'sequential':
                // Check that earnings didn't exceed reasonable limits
                const maxEarningsPerUser = 0.1; // 0.1 ETH max per user
                const avgEarningsPerUser = metrics.totalTokensEarned / metrics.totalUsers;
                
                if (avgEarningsPerUser > maxEarningsPerUser) {
                    return { valid: false, error: `Excessive earnings per user: ${avgEarningsPerUser.toFixed(4)} ETH` };
                }
                break;

            case 'rapid_purchase':
                // Check that rapid purchases were handled properly
                const totalPurchaseValue = metrics.totalPurchaseValue;
                const userCount = metrics.totalUsers;
                
                // Whales shouldn't be able to drain treasury too quickly
                if (totalPurchaseValue > userCount * 0.5) { // Max 0.5 ETH per whale
                    return { valid: false, error: `Excessive rapid purchase volume: ${totalPurchaseValue.toFixed(4)} ETH` };
                }
                break;

            case 'sybil':
                // Check for Sybil attack protection
                const treasuryLoss = scenario.metrics.treasuryChange;
                
                // Should limit losses even with many accounts
                if (treasuryLoss < -0.5) { // Lost more than 0.5 ETH
                    return { valid: false, error: `Excessive treasury loss in Sybil attack: ${treasuryLoss.toFixed(4)} ETH` };
                }
                break;

            case 'creator_abuse':
                // Check creator reward abuse protection
                const creatorRewards = this.framework.treasuryMetrics.creatorRewardsDistributed;
                const totalPurchases = metrics.totalPurchaseValue;
                
                // Creator rewards should be reasonable percentage of purchases
                const rewardPercentage = (creatorRewards / totalPurchases) * 100;
                if (rewardPercentage > 25) { // Max 25% of purchases go to creator rewards
                    return { valid: false, error: `Excessive creator reward percentage: ${rewardPercentage.toFixed(2)}%` };
                }
                break;
        }

        return { valid: true, message: 'Attack protection validated successfully' };
    }

    /**
     * Generate final test report
     */
    generateFinalReport(startTime) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        const totalTests = this.testSuites.reduce((sum, suite) => sum + suite.results.length, 0);
        const passedTests = this.testSuites.reduce((sum, suite) => 
            sum + suite.results.filter(r => r.success).length, 0
        );
        const failedTests = totalTests - passedTests;

        const report = {
            timestamp: Date.now(),
            duration: duration,
            summary: {
                totalTests: totalTests,
                passedTests: passedTests,
                failedTests: failedTests,
                successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
                totalScenarios: this.framework.testResults.scenarios.length,
                totalUsers: this.framework.testResults.scenarios.reduce((sum, s) => sum + s.users.length, 0),
                totalSessions: this.framework.testResults.scenarios.reduce((sum, s) => sum + s.sessions.length, 0)
            },
            treasuryAnalysis: {
                initialBalance: this.framework.treasuryMetrics.initialBalance,
                finalBalance: this.framework.treasuryMetrics.currentBalance,
                netChange: this.framework.treasuryMetrics.currentBalance - this.framework.treasuryMetrics.initialBalance,
                totalInflows: this.framework.treasuryMetrics.totalInflows,
                totalOutflows: this.framework.treasuryMetrics.totalOutflows,
                creatorRewardsDistributed: this.framework.treasuryMetrics.creatorRewardsDistributed,
                playerRewardsDistributed: this.framework.treasuryMetrics.playerRewardsDistributed
            },
            testSuites: this.testSuites.map(suite => ({
                name: suite.name,
                description: suite.description,
                success: suite.success,
                duration: suite.duration,
                testResults: suite.results.map(result => ({
                    name: result.name,
                    success: result.success,
                    duration: result.duration,
                    errors: result.errors,
                    warnings: result.warnings || [],
                    metrics: result.scenario?.metrics || {}
                }))
            })),
            recommendations: this.generateRecommendations(),
            risks: this.identifyRisks()
        };

        return report;
    }

    /**
     * Generate recommendations based on test results
     */
    generateRecommendations() {
        const recommendations = [];

        // Analyze treasury health
        const treasuryChange = this.framework.treasuryMetrics.currentBalance - this.framework.treasuryMetrics.initialBalance;
        if (treasuryChange < 0) {
            recommendations.push({
                priority: 'HIGH',
                category: 'Treasury Health',
                message: 'Treasury is losing ETH. Consider adjusting reward rates or implementing stronger earning limits.',
                suggestedAction: 'Review and reduce token earning rates, especially for grinders.'
            });
        }

        // Analyze creator rewards
        const creatorRewardRatio = this.framework.treasuryMetrics.creatorRewardsDistributed / this.framework.treasuryMetrics.totalInflows;
        if (creatorRewardRatio > 0.2) {
            recommendations.push({
                priority: 'MEDIUM',
                category: 'Creator Economics',
                message: 'Creator rewards are consuming a high percentage of revenue.',
                suggestedAction: 'Consider reducing creator reward percentage or implementing volume-based limits.'
            });
        }

        // Analyze attack protection
        const failedAttackTests = this.testSuites
            .find(s => s.name === 'Attack Scenarios')?.results
            .filter(r => !r.success) || [];

        if (failedAttackTests.length > 0) {
            recommendations.push({
                priority: 'CRITICAL',
                category: 'Security',
                message: 'Some attack protection mechanisms are failing.',
                suggestedAction: 'Implement stronger rate limiting, earning caps, and Sybil detection.'
            });
        }

        return recommendations;
    }

    /**
     * Identify risks based on test results
     */
    identifyRisks() {
        const risks = [];

        // Treasury drain risk
        const treasuryLoss = this.framework.treasuryMetrics.initialBalance - this.framework.treasuryMetrics.currentBalance;
        if (treasuryLoss > 0.1) { // Lost more than 0.1 ETH
            risks.push({
                level: 'HIGH',
                type: 'Treasury Drain',
                description: `Treasury lost ${treasuryLoss.toFixed(4)} ETH during testing`,
                impact: 'Could lead to insolvency if continued in production',
                mitigation: 'Implement earning limits and adjust reward mechanics'
            });
        }

        // Economic imbalance risk
        const netFlow = this.framework.treasuryMetrics.totalInflows - this.framework.treasuryMetrics.totalOutflows;
        if (netFlow < -0.05) { // Negative net flow
            risks.push({
                level: 'MEDIUM',
                type: 'Economic Imbalance',
                description: 'System is paying out more than it receives',
                impact: 'Unsustainable economic model',
                mitigation: 'Adjust reward rates or increase purchase incentives'
            });
        }

        return risks;
    }

    /**
     * Save report to file
     */
    async saveReport(report) {
        const reportsDir = path.join(process.cwd(), 'test', 'tokenomics', 'reports');
        
        // Create reports directory if it doesn't exist
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        const filename = `tokenomics-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        const filepath = path.join(reportsDir, filename);

        try {
            fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
            console.log(`📄 Test report saved: ${filepath}`);
        } catch (error) {
            console.error('❌ Failed to save test report:', error);
        }
    }
}

/**
 * Main execution function
 */
async function runTokenomicsTests() {
    console.log('🚀 Starting Tokenomics Test Suite...');
    
    const runner = new TokenomicsTestRunner();
    
    try {
        // Initialize the test runner
        await runner.initialize();
        
        // Run all tests
        const report = await runner.runAllTests();
        
        // Print summary
        console.log('\n' + '='.repeat(60));
        console.log('📊 TOKENOMICS TEST SUMMARY');
        console.log('='.repeat(60));
        console.log(`Total Tests: ${report.summary.totalTests}`);
        console.log(`Passed: ${report.summary.passedTests} (${report.summary.successRate.toFixed(1)}%)`);
        console.log(`Failed: ${report.summary.failedTests}`);
        console.log(`Duration: ${(report.duration / 1000).toFixed(1)} seconds`);
        console.log('');
        console.log('💰 Treasury Analysis:');
        console.log(`Initial Balance: ${report.treasuryAnalysis.initialBalance} ETH`);
        console.log(`Final Balance: ${report.treasuryAnalysis.finalBalance} ETH`);
        console.log(`Net Change: ${report.treasuryAnalysis.netChange > 0 ? '+' : ''}${report.treasuryAnalysis.netChange.toFixed(4)} ETH`);
        console.log(`Total Inflows: ${report.treasuryAnalysis.totalInflows.toFixed(4)} ETH`);
        console.log(`Total Outflows: ${report.treasuryAnalysis.totalOutflows.toFixed(4)} ETH`);
        console.log('');
        
        if (report.recommendations.length > 0) {
            console.log('⚠️  Recommendations:');
            report.recommendations.forEach(rec => {
                console.log(`[${rec.priority}] ${rec.category}: ${rec.message}`);
                console.log(`   Action: ${rec.suggestedAction}`);
            });
            console.log('');
        }
        
        if (report.risks.length > 0) {
            console.log('🚨 Identified Risks:');
            report.risks.forEach(risk => {
                console.log(`[${risk.level}] ${risk.type}: ${risk.description}`);
                console.log(`   Impact: ${risk.impact}`);
                console.log(`   Mitigation: ${risk.mitigation}`);
            });
        }
        
        console.log('\n✅ Tokenomics testing completed successfully!');
        
        return report;
        
    } catch (error) {
        console.error('❌ Tokenomics testing failed:', error