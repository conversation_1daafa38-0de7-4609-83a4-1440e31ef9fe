import { ethers } from 'ethers';
import { TokenEconomyManager } from '../../src/managers/TokenEconomyManager.js';

/**
 * Tokenomics Test Framework - Simulates real users making on-chain transactions
 * Uses Hardhat local node with real test ETH for treasury drain testing
 */
export class TokenomicsTestFramework {
    constructor() {
        // Hardhat local node configuration
        this.hardhatProvider = new ethers.providers.JsonRpcProvider('http://127.0.0.1:8545');
        
        // Test accounts from Hardhat (pre-funded with 10,000 ETH each)
        this.testAccounts = [
            {
                address: '******************************************',
                privateKey: '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
            },
            {
                address: '******************************************',
                privateKey: '0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d'
            },
            {
                address: '******************************************',
                privateKey: '0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a'
            },
            {
                address: '******************************************',
                privateKey: '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
            },
            {
                address: '******************************************',
                privateKey: '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
            }
        ];

        // Hot wallet configuration (Account #18)
        this.hotWallet = {
            address: '******************************************',
            privateKey: '0xde9be858da4a475276426320d5e9262ecfc3ba460bfac56360bfa6c4c28b4ee0'
        };

        // Game server API configuration
        this.gameServerUrl = 'http://localhost:3001';
        
        // Treasury tracking
        this.treasuryMetrics = {
            initialBalance: 0,
            currentBalance: 0,
            totalInflows: 0,
            totalOutflows: 0,
            creatorRewardsDistributed: 0,
            playerRewardsDistributed: 0,
            transactions: []
        };

        // User behavior models
        this.userBehaviors = {
            grinder: new GrinderBehavior(),
            casual: new CasualPlayerBehavior(),
            whale: new WhaleBehavior(),
            creator: new CreatorBehavior()
        };

        // Test results tracking
        this.testResults = {
            scenarios: [],
            metrics: {},
            failures: []
        };

        // Real-time monitoring
        this.monitoring = {
            startTime: null,
            intervalId: null,
            logs: []
        };
    }

    /**
     * Initialize the test framework and connect to Hardhat node
     */
    async initialize() {
        console.log('🚀 Initializing Tokenomics Test Framework...');
        
        try {
            // Test Hardhat connection
            const blockNumber = await this.hardhatProvider.getBlockNumber();
            console.log(`✅ Connected to Hardhat node at block ${blockNumber}`);

            // Get initial hot wallet balance
            const hotWalletBalance = await this.hardhatProvider.getBalance(this.hotWallet.address);
            this.treasuryMetrics.initialBalance = ethers.utils.formatEther(hotWalletBalance);
            this.treasuryMetrics.currentBalance = this.treasuryMetrics.initialBalance;
            
            console.log(`💰 Hot wallet initial balance: ${this.treasuryMetrics.initialBalance} ETH`);

            // Test game server connection
            await this.testGameServerConnection();

            console.log('✅ Tokenomics Test Framework initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize test framework:', error);
            throw error;
        }
    }

    /**
     * Test game server connection
     */
    async testGameServerConnection() {
        try {
            const response = await fetch(`${this.gameServerUrl}/api/health`);
            if (response.ok) {
                console.log('✅ Game server connection successful');
            } else {
                throw new Error(`Game server returned ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Game server connection failed:', error);
            throw error;
        }
    }

    /**
     * Create a simulated user with wallet and behavior model
     */
    async createSimulatedUser(accountIndex, behaviorType) {
        const account = this.testAccounts[accountIndex];
        if (!account) {
            throw new Error(`Invalid account index: ${accountIndex}`);
        }

        const behavior = this.userBehaviors[behaviorType];
        if (!behavior) {
            throw new Error(`Invalid behavior type: ${behaviorType}`);
        }

        // Create wallet instance
        const wallet = new ethers.Wallet(account.privateKey, this.hardhatProvider);

        const user = {
            id: `user_${accountIndex}_${behaviorType}`,
            address: account.address,
            wallet: wallet,
            behavior: behavior,
            sessionData: {
                gamesPlayed: 0,
                totalScore: 0,
                tokensEarned: 0,
                purchasesMade: [],
                creatorRewardsReceived: 0
            }
        };

        console.log(`👤 Created simulated user: ${user.id} (${behaviorType}) with address: ${user.address}`);
        return user;
    }

    /**
     * Simulate user playing the game and earning tokens
     */
    async simulateGameSession(user, sessionConfig = {}) {
        console.log(`🎮 Starting game session for ${user.id}...`);
        
        const session = {
            userId: user.id,
            startTime: Date.now(),
            actions: [],
            tokensEarned: 0,
            purchasesMade: []
        };

        try {
            // Simulate game play based on user behavior
            const gameActions = await user.behavior.generateGameActions(sessionConfig);
            
            for (const action of gameActions) {
                await this.executeGameAction(user, action, session);
                await this.delay(100); // Small delay between actions
            }

            // Update user session data
            user.sessionData.gamesPlayed++;
            user.sessionData.tokensEarned += session.tokensEarned;

            session.endTime = Date.now();
            session.duration = session.endTime - session.startTime;

            console.log(`✅ Game session completed for ${user.id}: ${session.tokensEarned} tokens earned in ${session.duration}ms`);
            return session;

        } catch (error) {
            console.error(`❌ Game session failed for ${user.id}:`, error);
            throw error;
        }
    }

    /**
     * Execute a single game action
     */
    async executeGameAction(user, action, session) {
        switch (action.type) {
            case 'level_completion':
                await this.simulateLevelCompletion(user, action.data, session);
                break;
            case 'purchase':
                await this.simulatePurchase(user, action.data, session);
                break;
            case 'creator_purchase':
                await this.simulateCreatorPurchase(user, action.data, session);
                break;
            default:
                console.warn(`Unknown action type: ${action.type}`);
        }
    }

    /**
     * Simulate level completion and token earning
     */
    async simulateLevelCompletion(user, levelData, session) {
        console.log(`🎯 Simulating level completion for ${user.id}: Level ${levelData.levelNumber}`);
        
        try {
            // Make API call to game server to report level completion
            const response = await fetch(`${this.gameServerUrl}/api/game/level-complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${user.address}`
                },
                body: JSON.stringify({
                    walletAddress: user.address,
                    levelNumber: levelData.levelNumber,
                    completionTime: levelData.completionTime,
                    score: levelData.score,
                    bonuses: levelData.bonuses,
                    sessionId: session.id
                })
            });

            if (!response.ok) {
                throw new Error(`Level completion API failed: ${response.status}`);
            }

            const result = await response.json();
            const tokensEarned = result.tokensAwarded || 0;

            session.tokensEarned += tokensEarned;
            session.actions.push({
                type: 'level_completion',
                levelNumber: levelData.levelNumber,
                tokensEarned: tokensEarned,
                timestamp: Date.now()
            });

            // Track treasury outflow
            this.treasuryMetrics.totalOutflows += tokensEarned;
            this.treasuryMetrics.playerRewardsDistributed += tokensEarned;

            console.log(`💰 Level completion: ${tokensEarned} tokens awarded to ${user.id}`);

        } catch (error) {
            console.error(`❌ Level completion simulation failed for ${user.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate purchase transaction
     */
    async simulatePurchase(user, purchaseData, session) {
        console.log(`🛒 Simulating purchase for ${user.id}: ${purchaseData.itemType} for ${purchaseData.cost} ETH`);
        
        try {
            // Send actual transaction from user's wallet to hot wallet
            const tx = await user.wallet.sendTransaction({
                to: this.hotWallet.address,
                value: ethers.utils.parseEther(purchaseData.cost.toString()),
                gasLimit: 21000
            });

            // Wait for transaction confirmation
            const receipt = await tx.wait();
            
            // Make API call to game server to process purchase
            const response = await fetch(`${this.gameServerUrl}/api/store/purchase`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${user.address}`
                },
                body: JSON.stringify({
                    walletAddress: user.address,
                    itemType: purchaseData.itemType,
                    itemId: purchaseData.itemId,
                    cost: purchaseData.cost,
                    transactionHash: tx.hash,
                    sessionId: session.id
                })
            });

            if (!response.ok) {
                throw new Error(`Purchase API failed: ${response.status}`);
            }

            session.purchasesMade.push({
                itemType: purchaseData.itemType,
                cost: purchaseData.cost,
                transactionHash: tx.hash,
                timestamp: Date.now()
            });

            // Track treasury inflow
            this.treasuryMetrics.totalInflows += purchaseData.cost;
            this.treasuryMetrics.transactions.push({
                type: 'purchase',
                from: user.address,
                amount: purchaseData.cost,
                transactionHash: tx.hash,
                timestamp: Date.now()
            });

            console.log(`✅ Purchase completed: ${purchaseData.cost} ETH from ${user.id} (tx: ${tx.hash})`);

        } catch (error) {
            console.error(`❌ Purchase simulation failed for ${user.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate creator environment purchase (tests creator rewards)
     */
    async simulateCreatorPurchase(user, purchaseData, session) {
        console.log(`🎨 Simulating creator environment purchase for ${user.id}: Environment ${purchaseData.environmentId} from creator ${purchaseData.creatorAddress}`);
        
        try {
            // Send purchase transaction
            const tx = await user.wallet.sendTransaction({
                to: this.hotWallet.address,
                value: ethers.utils.parseEther(purchaseData.cost.toString()),
                gasLimit: 21000
            });

            await tx.wait();

            // Make API call to purchase creator environment
            const response = await fetch(`${this.gameServerUrl}/api/environments/purchase`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${user.address}`
                },
                body: JSON.stringify({
                    walletAddress: user.address,
                    environmentId: purchaseData.environmentId,
                    creatorAddress: purchaseData.creatorAddress,
                    cost: purchaseData.cost,
                    transactionHash: tx.hash,
                    sessionId: session.id
                })
            });

            if (!response.ok) {
                throw new Error(`Creator purchase API failed: ${response.status}`);
            }

            const result = await response.json();
            
            // Track creator reward if distributed
            if (result.creatorReward) {
                this.treasuryMetrics.creatorRewardsDistributed += result.creatorReward;
                console.log(`🎁 Creator reward distributed: ${result.creatorReward} ETH to ${purchaseData.creatorAddress}`);
            }

            session.purchasesMade.push({
                itemType: 'creator_environment',
                cost: purchaseData.cost,
                creatorAddress: purchaseData.creatorAddress,
                creatorReward: result.creatorReward,
                transactionHash: tx.hash,
                timestamp: Date.now()
            });

            // Track treasury metrics
            this.treasuryMetrics.totalInflows += purchaseData.cost;
            this.treasuryMetrics.transactions.push({
                type: 'creator_purchase',
                from: user.address,
                to: purchaseData.creatorAddress,
                amount: purchaseData.cost,
                creatorReward: result.creatorReward,
                transactionHash: tx.hash,
                timestamp: Date.now()
            });

            console.log(`✅ Creator environment purchase completed: ${purchaseData.cost} ETH from ${user.id} to creator ${purchaseData.creatorAddress}`);

        } catch (error) {
            console.error(`❌ Creator purchase simulation failed for ${user.id}:`, error);
            throw error;
        }
    }

    /**
     * Update treasury balance from blockchain
     */
    async updateTreasuryBalance() {
        try {
            const balance = await this.hardhatProvider.getBalance(this.hotWallet.address);
            this.treasuryMetrics.currentBalance = ethers.utils.formatEther(balance);
            
            console.log(`💰 Updated treasury balance: ${this.treasuryMetrics.currentBalance} ETH`);
            return this.treasuryMetrics.currentBalance;
        } catch (error) {
            console.error('❌ Failed to update treasury balance:', error);
            throw error;
        }
    }

    /**
     * Start real-time monitoring
     */
    startMonitoring(intervalMs = 5000) {
        this.monitoring.startTime = Date.now();
        
        this.monitoring.intervalId = setInterval(async () => {
            await this.updateTreasuryBalance();
            
            const netFlow = this.treasuryMetrics.totalInflows - this.treasuryMetrics.totalOutflows;
            const runTime = (Date.now() - this.monitoring.startTime) / 1000 / 60; // minutes
            
            const logEntry = {
                timestamp: Date.now(),
                treasuryBalance: this.treasuryMetrics.currentBalance,
                totalInflows: this.treasuryMetrics.totalInflows,
                totalOutflows: this.treasuryMetrics.totalOutflows,
                netFlow: netFlow,
                creatorRewards: this.treasuryMetrics.creatorRewardsDistributed,
                playerRewards: this.treasuryMetrics.playerRewardsDistributed,
                runTimeMinutes: runTime
            };

            this.monitoring.logs.push(logEntry);
            
            console.log(`📊 Treasury: ${this.treasuryMetrics.currentBalance} ETH | Net Flow: ${netFlow.toFixed(4)} ETH | Runtime: ${runTime.toFixed(1)}min`);
            
            // Alert if treasury is being drained
            if (this.treasuryMetrics.currentBalance < this.treasuryMetrics.initialBalance * 0.5) {
                console.warn('🚨 ALERT: Treasury balance below 50% of initial!');
            }
            
        }, intervalMs);

        console.log(`📈 Started real-time monitoring (interval: ${intervalMs}ms)`);
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        if (this.monitoring.intervalId) {
            clearInterval(this.monitoring.intervalId);
            this.monitoring.intervalId = null;
            console.log('⏹️ Stopped real-time monitoring');
        }
    }

    /**
     * Run a complete test scenario
     */
    async runTestScenario(scenarioConfig) {
        console.log(`🧪 Running test scenario: ${scenarioConfig.name}`);
        
        const scenario = {
            name: scenarioConfig.name,
            startTime: Date.now(),
            users: [],
            sessions: [],
            metrics: {},
            success: true,
            errors: []
        };

        try {
            // Create users for this scenario
            for (let i = 0; i < scenarioConfig.userCount; i++) {
                const behaviorType = scenarioConfig.behaviorTypes[i % scenarioConfig.behaviorTypes.length];
                const user = await this.createSimulatedUser(i, behaviorType);
                scenario.users.push(user);
            }

            // Start monitoring
            this.startMonitoring();

            // Run user sessions
            for (const user of scenario.users) {
                try {
                    const sessionConfig = scenarioConfig.sessionConfig || {};
                    const session = await this.simulateGameSession(user, sessionConfig);
                    scenario.sessions.push(session);
                    
                    // Small delay between users
                    await this.delay(1000);
                } catch (error) {
                    console.error(`❌ Session failed for ${user.id}:`, error);
                    scenario.errors.push({ userId: user.id, error: error.message });
                    scenario.success = false;
                }
            }

            // Final treasury update
            await this.updateTreasuryBalance();
            
            // Calculate scenario metrics
            scenario.metrics = this.calculateScenarioMetrics(scenario);
            
            scenario.endTime = Date.now();
            scenario.duration = scenario.endTime - scenario.startTime;

            console.log(`✅ Test scenario completed: ${scenario.name} (${scenario.duration}ms)`);
            
        } catch (error) {
            console.error(`❌ Test scenario failed: ${scenario.name}`, error);
            scenario.success = false;
            scenario.errors.push({ scenario: scenario.name, error: error.message });
        } finally {
            this.stopMonitoring();
        }

        this.testResults.scenarios.push(scenario);
        return scenario;
    }

    /**
     * Calculate scenario metrics
     */
    calculateScenarioMetrics(scenario) {
        const totalTokensEarned = scenario.sessions.reduce((sum, session) => sum + session.tokensEarned, 0);
        const totalPurchases = scenario.sessions.reduce((sum, session) => sum + session.purchasesMade.length, 0);
        const totalPurchaseValue = scenario.sessions.reduce((sum, session) => {
            return sum + session.purchasesMade.reduce((purchaseSum, purchase) => purchaseSum + purchase.cost, 0);
        }, 0);

        return {
            totalUsers: scenario.users.length,
            totalSessions: scenario.sessions.length,
            totalTokensEarned: totalTokensEarned,
            totalPurchases: totalPurchases,
            totalPurchaseValue: totalPurchaseValue,
            averageTokensPerUser: totalTokensEarned / scenario.users.length,
            averagePurchaseValue: totalPurchases > 0 ? totalPurchaseValue / totalPurchases : 0,
            treasuryChange: this.treasuryMetrics.currentBalance - this.treasuryMetrics.initialBalance,
            netFlow: this.treasuryMetrics.totalInflows - this.treasuryMetrics.totalOutflows
        };
    }

    /**
     * Generate comprehensive test report
     */
    generateTestReport() {
        const report = {
            timestamp: Date.now(),
            duration: this.monitoring.startTime ? Date.now() - this.monitoring.startTime : 0,
            treasuryMetrics: { ...this.treasuryMetrics },
            scenarios: this.testResults.scenarios.map(scenario => ({
                name: scenario.name,
                success: scenario.success,
                duration: scenario.duration,
                metrics: scenario.metrics,
                errorCount: scenario.errors.length
            })),
            monitoringLogs: this.monitoring.logs,
            summary: this.generateSummary()
        };

        return report;
    }

    /**
     * Generate test summary
     */
    generateSummary() {
        const totalScenarios = this.testResults.scenarios.length;
        const successfulScenarios = this.testResults.scenarios.filter(s => s.success).length;
        const totalUsers = this.testResults.scenarios.reduce((sum, s) => sum + s.users.length, 0);
        const totalSessions = this.testResults.scenarios.reduce((sum, s) => sum + s.sessions.length, 0);

        return {
            totalScenarios: totalScenarios,
            successfulScenarios: successfulScenarios,
            failedScenarios: totalScenarios - successfulScenarios,
            successRate: totalScenarios > 0 ? (successfulScenarios / totalScenarios) * 100 : 0,
            totalUsers: totalUsers,
            totalSessions: totalSessions,
            finalTreasuryBalance: this.treasuryMetrics.currentBalance,
            treasuryChange: this.treasuryMetrics.currentBalance - this.treasuryMetrics.initialBalance,
            totalInflows: this.treasuryMetrics.totalInflows,
            totalOutflows: this.treasuryMetrics.totalOutflows,
            netFlow: this.treasuryMetrics.totalInflows - this.treasuryMetrics.totalOutflows,
            creatorRewardsDistributed: this.treasuryMetrics.creatorRewardsDistributed,
            playerRewardsDistributed: this.treasuryMetrics.playerRewardsDistributed
        };
    }

    /**
     * Utility: Delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * User Behavior Models
 */
class GrinderBehavior {
    constructor() {
        this.type = 'grinder';
        this.description = 'Plays extensively, earns maximum tokens, minimal spending';
    }

    async generateGameActions(sessionConfig) {
        const actions = [];
        const numLevels = sessionConfig.numLevels || 10;

        // Grinders play many levels to maximize earnings
        for (let i = 1; i <= numLevels; i++) {
            actions.push({
                type: 'level_completion',
                data: {
                    levelNumber: i,
                    completionTime: 45, // Fast completion for time bonus
                    score: 1500 + (i * 100), // High scores
                    bonuses: {
                        speed: true,
                        accuracy: true,
                        perfect: Math.random() > 0.3 // 70% perfect completion rate
                    }
                }
            });
        }

        // Minimal purchases (5% chance per session)
        if (Math.random() < 0.05) {
            actions.push({
                type: 'purchase',
                data: {
                    itemType: 'consumable',
                    itemId: 'basic-boost',
                    cost: 0.001
                }
            });
        }

        return actions;
    }
}

class CasualPlayerBehavior {
    constructor() {
        this.type = 'casual';
        this.description = 'Moderate play, some purchases, balanced approach';
    }

    async generateGameActions(sessionConfig) {
        const actions = [];
        const numLevels = sessionConfig.numLevels || 5;

        // Casual players play moderate number of levels
        for (let i = 1; i <= numLevels; i++) {
            actions.push({
                type: 'level_completion',
                data: {
                    levelNumber: i,
                    completionTime: 60, // Average completion time
                    score: 1000 + (i * 50),
                    bonuses: {
                        speed: Math.random() > 0.5,
                        accuracy: Math.random() > 0.6,
                        perfect: Math.random() > 0.7
                    }
                }
            });
        }

        // Some purchases (30% chance per session)
        if (Math.random() < 0.3) {
            actions.push({
                type: 'purchase',
                data: {
                    itemType: 'consumable',
                    itemId: 'premium-boost',
                    cost: 0.005
                }
            });
        }

        return actions;
    }
}

class WhaleBehavior {
    constructor() {
        this.type = 'whale';
        this.description = 'Heavy purchaser, minimal grinding, large transactions';
    }

    async generateGameActions(sessionConfig) {
        const actions = [];
        const numLevels = sessionConfig.numLevels || 2;

        // Whales play few levels
        for (let i = 1; i <= numLevels; i++) {
            actions.push({
                type: 'level_completion',
                data: {
                    levelNumber: i,
                    completionTime: 90, // Slower completion
                    score: 800 + (i * 25),
                    bonuses: {
                        speed: false,
                        accuracy: Math.random() > 0.4,
                        perfect: Math.random() > 0.8
                    }
                }
            });
        }

        // High-value purchases (80% chance per session)
        if (Math.random() < 0.8) {
            actions.push({
                type: 'purchase',
                data: {
                    itemType: 'premium',
                    itemId: 'mega-boost',
                    cost: 0.05
                }
            });
        }

        // Chance to purchase creator environments
        if (Math.random() < 0.4) {
            actions.push({
                type: 'creator_purchase',
                data: {
                    environmentId: 'test-environment-1',
                    creatorAddress: '0x8626f6940e2eb28930efb4cef49b2d1f2c9c1199', // Account #19
                    cost: 0.1
                }
            });
        }

        return actions;
    }
}

class CreatorBehavior {
    constructor() {
        this.type = 'creator';
        this.description = 'Content creation, receives creator rewards';
    }

    async generateGameActions(sessionConfig) {
        const actions = [];

        // Creators might play some levels
        const numLevels = Math.floor(Math.random() * 3) + 1;
        for (let i = 1; i <= numLevels; i++) {
            actions.push({
                type: 'level_completion',
                data: {
                    levelNumber: i,
                    completionTime: 75,
                    score: 1200 + (i * 75),
                    bonuses: {
                        speed: Math.random() > 0.6,
                        accuracy: Math.random() > 0.7,
                        perfect: Math.random() > 0.5
                    }
                }
            });
        }

        // Creators are more likely to purchase other creators' environments
        if (Math.random() < 0.6) {
            actions.push({
                type: 'creator_purchase',
                data: {
                    environmentId: 'test-environment-2',
                    creatorAddress: '0x8626f6940e2eb28930efb4cef49b2d1f2c9c1199',
                    cost: 0.08
                }
            });
        }

        return actions;
    }
}