import { ethers } from 'ethers';
import fetch from 'node-fetch';

/**
 * ON-CHAIN Treasury Drain Protection Test
 * 
 * This test simulates REAL USERS making ACTUAL ON-CHAIN TRANSACTIONS
 * through your game server to test treasury drain protection.
 * 
 * - Uses actual wallets from Hardhat local node
 * - Makes real ETH transactions to hot wallet
 * - Calls actual game server APIs
 * - Tests creator rewards for Mystical Environment purchases
 * - Monitors treasury balance changes ON-CHAIN
 */

describe('ON-CHAIN Treasury Drain Protection', () => {
    let provider;
    let hotWallet;
    let testUsers;
    let gameServerUrl;
    let initialTreasuryBalance;

    // Test configuration - ACTUAL HARDHAT ACCOUNTS
    const HARDHAT_ACCOUNTS = [
        {
            address: '******************************************',
            privateKey: '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
        },
        {
            address: '******************************************',
            privateKey: '0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d'
        },
        {
            address: '******************************************',
            privateKey: '0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a'
        },
        {
            address: '******************************************',
            privateKey: '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
        },
        {
            address: '******************************************',
            privateKey: '0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a'
        }
    ];

    // ACTUAL HOT WALLET FROM YOUR HARDHAT NODE
    const HOT_WALLET = {
        address: '******************************************',
        privateKey: '0xde9be858da4a475276426320d5e9262ecfc3ba460bfac56360bfa6c4c28b4ee0'
    };

    beforeAll(async () => {
        // Connect to YOUR local Hardhat node
        provider = new ethers.JsonRpcProvider('http://localhost:8545');
        gameServerUrl = 'http://localhost:3001';
        
        // Initialize hot wallet with ACTUAL private key
        hotWallet = new ethers.Wallet(HOT_WALLET.privateKey, provider);
        
        // Initialize test users with ACTUAL wallets from Hardhat
        testUsers = HARDHAT_ACCOUNTS.map(account => ({
            address: account.address,
            wallet: new ethers.Wallet(account.privateKey, provider),
            userId: account.address // Use wallet address as userId
        }));

        // Get ACTUAL treasury balance ON-CHAIN
        initialTreasuryBalance = await provider.getBalance(HOT_WALLET.address);
        console.log(`🏦 ACTUAL Treasury Balance: ${ethers.formatEther(initialTreasuryBalance)} ETH`);

        // Verify YOUR game server is running
        try {
            const healthResponse = await fetch(`${gameServerUrl}/api/health`);
            if (!healthResponse.ok) {
                throw new Error('Game server health check failed');
            }
            console.log('✅ YOUR Game server is running and accessible');
        } catch (error) {
            throw new Error(`YOUR Game server not accessible: ${error.message}. Make sure to run: node server/index.js`);
        }
    });

    /**
     * Test REAL user behavior patterns that could drain treasury
     */
    describe('REAL User Behavior Simulation', () => {
        
        test('Grinder User - Earns through game functions, spends nothing', async () => {
            const grinder = testUsers[0];
            console.log(`🎮 Testing REAL Grinder User: ${grinder.address}`);
            
            const initialBalance = await provider.getBalance(HOT_WALLET.address);
            
            // Simulate grinder earning through ACTUAL game server calls
            // Grinders earn max possible (100%, not 85%) and spend NOTHING (not 5%)
            const earningResults = [];
            
            // Simulate 10 gameplay sessions where grinder earns maximum
            for (let i = 0; i < 10; i++) {
                const earningResult = await makeGameplayEarning(grinder, {
                    amount: 0.001, // 0.001 ETH per session (max earning)
                    reason: `gameplay_session_${i + 1}`
                });
                
                if (earningResult.success) {
                    earningResults.push(earningResult);
                }
                
                // Small delay between sessions
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.log(`💰 Grinder completed ${earningResults.length} earning sessions`);
            
            // Check treasury balance - should not be drained
            const finalBalance = await provider.getBalance(HOT_WALLET.address);
            const balanceChange = finalBalance - initialBalance;
            
            console.log(`🏦 Treasury balance change: ${ethers.formatEther(balanceChange)} ETH`);
            
            // Treasury should not be significantly drained by grinder alone
            expect(finalBalance).toBeGreaterThan(ethers.parseEther('9.9')); // Should still have most of initial balance
        });

        test('Creator Environment Purchase - ACTUAL on-chain creator reward', async () => {
            const buyer = testUsers[1];
            const creator = testUsers[2];
            
            console.log(`🎨 Testing REAL Creator Environment Purchase ON-CHAIN`);
            console.log(`👤 Buyer: ${buyer.address}`);
            console.log(`🎭 Creator: ${creator.address}`);
            
            // Create ACTUAL environment through YOUR server
            const environment = await createRealEnvironment(creator);
            expect(environment).toBeDefined();
            expect(environment.id).toBeDefined();
            console.log(`🌍 Created environment: ${environment.id}`);
            
            // Record ACTUAL on-chain balances before purchase
            const initialBuyerBalance = await provider.getBalance(buyer.address);
            const initialCreatorBalance = await provider.getBalance(creator.address);
            const initialTreasuryBalance = await provider.getBalance(HOT_WALLET.address);
            
            console.log(`💰 Initial balances:`);
            console.log(`  Buyer: ${ethers.formatEther(initialBuyerBalance)} ETH`);
            console.log(`  Creator: ${ethers.formatEther(initialCreatorBalance)} ETH`);
            console.log(`  Treasury: ${ethers.formatEther(initialTreasuryBalance)} ETH`);
            
            // Buyer makes ACTUAL ETH transaction and purchases environment
            const purchaseResult = await makeRealEnvironmentPurchase(buyer, {
                environmentId: environment.id,
                creatorAddress: creator.address,
                cost: 0.01 // 0.01 ETH
            });

            console.log('Purchase result:', purchaseResult);
            expect(purchaseResult.success).toBe(true);
            expect(purchaseResult.transactionHash).toBeDefined();
            console.log(`✅ Purchase transaction: ${purchaseResult.transactionHash}`);
            
            // Wait for creator reward to be distributed ON-CHAIN
            console.log(`⏳ Waiting for creator reward distribution...`);
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Check ACTUAL on-chain balances after purchase
            const finalBuyerBalance = await provider.getBalance(buyer.address);
            const finalCreatorBalance = await provider.getBalance(creator.address);
            const finalTreasuryBalance = await provider.getBalance(HOT_WALLET.address);
            
            console.log(`💰 Final balances:`);
            console.log(`  Buyer: ${ethers.formatEther(finalBuyerBalance)} ETH`);
            console.log(`  Creator: ${ethers.formatEther(finalCreatorBalance)} ETH`);
            console.log(`  Treasury: ${ethers.formatEther(finalTreasuryBalance)} ETH`);
            
            // Verify buyer ACTUALLY paid (balance decreased)
            const buyerPaid = initialBuyerBalance - finalBuyerBalance;
            expect(buyerPaid).toBeGreaterThan(ethers.parseEther('0.009')); // At least 0.009 ETH (accounting for gas)
            console.log(`💸 Buyer paid: ${ethers.formatEther(buyerPaid)} ETH`);
            
            // Verify creator ACTUALLY received reward ON-CHAIN
            const creatorReward = finalCreatorBalance - initialCreatorBalance;
            const expectedReward = ethers.parseEther('0.005'); // 50% of 0.01 ETH
            expect(creatorReward).toBeGreaterThanOrEqual(expectedReward * BigInt(95) / BigInt(100)); // Allow 5% tolerance for gas
            console.log(`🎁 Creator received: ${ethers.formatEther(creatorReward)} ETH`);
            
            // Verify treasury received remaining amount ON-CHAIN
            const treasuryIncrease = finalTreasuryBalance - initialTreasuryBalance;
            expect(treasuryIncrease).toBeGreaterThan(ethers.parseEther('0.004')); // Should get remaining ~40%
            console.log(`🏦 Treasury increase: ${ethers.formatEther(treasuryIncrease)} ETH`);
            
            // Verify treasury is not being drained
            expect(finalTreasuryBalance).toBeGreaterThan(initialTreasuryBalance);
        });

        test('Multi-User Coordinated Attack - Treasury Protection', async () => {
            console.log(`⚔️ Testing REAL Multi-User Attack ON-CHAIN`);
            
            const attackers = testUsers.slice(0, 3);
            const initialTreasuryBalance = await provider.getBalance(HOT_WALLET.address);
            
            console.log(`🏦 Treasury balance before attack: ${ethers.formatEther(initialTreasuryBalance)} ETH`);
            
            // Launch coordinated attack with ACTUAL users making REAL transactions
            const attackPromises = attackers.map(async (attacker, index) => {
                console.log(`🔥 Launching attack from user ${index + 1}: ${attacker.address}`);
                return simulateRealAttack(attacker, {
                    attackType: 'rapid_earning_withdrawal',
                    intensity: 'maximum',
                    attempts: 20
                });
            });
            
            const attackResults = await Promise.all(attackPromises);
            
            // Check ACTUAL treasury balance after attack
            const finalTreasuryBalance = await provider.getBalance(HOT_WALLET.address);
            const treasuryChange = finalTreasuryBalance - initialTreasuryBalance;
            
            console.log(`🏦 Treasury balance after attack: ${ethers.formatEther(finalTreasuryBalance)} ETH`);
            console.log(`📊 Treasury change: ${ethers.formatEther(treasuryChange)} ETH`);
            
            // Treasury should NOT be drained - protection should work
            expect(finalTreasuryBalance).toBeGreaterThan(ethers.parseEther('9.0')); // Should retain most funds
            
            // Log attack results
            attackResults.forEach((result, index) => {
                console.log(`🔍 Attacker ${index + 1} results:`);
                console.log(`  Successful earnings: ${result.successfulEarnings}`);
                console.log(`  Blocked attempts: ${result.blockedAttempts}`);
                console.log(`  Total ETH earned: ${result.totalEthEarned}`);
            });
            
            // Verify attack protection worked
            const totalBlockedAttempts = attackResults.reduce((sum, result) => sum + result.blockedAttempts, 0);
            expect(totalBlockedAttempts).toBeGreaterThan(0); // Some attacks should be blocked
        });
    });

    /**
     * Helper Functions for REAL User Simulation
     */

    /**
     * Make ACTUAL gameplay earning through YOUR game server
     */
    async function makeGameplayEarning(user, config) {
        try {
            // Call YOUR actual game server API to award tokens
            const response = await fetch(`${gameServerUrl}/api/tokens/award`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer secure-token-for-development'
                },
                body: JSON.stringify({
                    userId: user.address,
                    amount: config.amount.toString(),
                    reason: config.reason,
                    metadata: {
                        walletAddress: user.address,
                        earnedThroughGameplay: true
                    }
                })
            });

            if (!response.ok) {
                console.error(`Earning failed: ${response.status}`);
                return { success: false, error: `API call failed: ${response.status}` };
            }

            const result = await response.json();
            console.log(`💰 User ${user.address.slice(-8)} earned ${config.amount} ETH: ${config.reason}`);
            return { success: true, ...result };

        } catch (error) {
            console.error('Error making gameplay earning:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Create REAL environment through YOUR server
     */
    async function createRealEnvironment(creator) {
        const environmentData = {
            name: `Mystical Test Environment ${Date.now()}`,
            description: 'A mystical environment for testing creator rewards',
            gameplayModifiers: {
                environmentType: 'mystical',
                difficulty: 'normal',
                specialEffects: ['creator_reward_eligible']
            }
        };

        try {
            const response = await fetch(`${gameServerUrl}/api/environments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    environmentData,
                    creatorUserId: creator.address
                })
            });

            if (!response.ok) {
                throw new Error(`Failed to create environment: ${response.status}`);
            }

            const result = await response.json();
            console.log(`🌍 Created environment ${result.environmentId} by creator ${creator.address.slice(-8)}`);
            return result.environment;

        } catch (error) {
            console.error('Error creating real environment:', error);
            throw error;
        }
    }

    /**
     * Make REAL environment purchase with ACTUAL ETH transaction
     */
    async function makeRealEnvironmentPurchase(buyer, purchaseData) {
        try {
            console.log(`💳 Making REAL ETH transaction: ${purchaseData.cost} ETH to ${HOT_WALLET.address}`);

            // Send ACTUAL ETH transaction to YOUR hot wallet
            const tx = await buyer.wallet.sendTransaction({
                to: HOT_WALLET.address,
                value: ethers.parseEther(purchaseData.cost.toString()),
                gasLimit: 21000
            });

            // Wait for ACTUAL transaction confirmation ON-CHAIN
            const receipt = await tx.wait();
            console.log(`✅ REAL transaction confirmed in block ${receipt.blockNumber}: ${tx.hash}`);

            // Call YOUR game server API to record purchase
            const purchaseResponse = await fetch(`${gameServerUrl}/api/environments/${purchaseData.environmentId}/purchase`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    purchaserUserId: buyer.address,
                    cost: purchaseData.cost
                })
            });

            if (!purchaseResponse.ok) {
                const errorText = await purchaseResponse.text();
                console.error(`Purchase recording failed: ${purchaseResponse.status} - ${errorText}`);
                throw new Error(`Purchase recording failed: ${purchaseResponse.status} - ${errorText}`);
            }

            const purchaseData = await purchaseResponse.json();
            console.log(`📝 Purchase recorded on server:`, purchaseData);

            // Trigger ACTUAL creator reward distribution through YOUR server
            const rewardResponse = await fetch(`${gameServerUrl}/api/rewards/distribute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer secure-token-for-development'
                },
                body: JSON.stringify({
                    creatorUserId: purchaseData.creatorAddress,
                    amount: (purchaseData.cost * 0.5).toString(), // 50% creator reward
                    reason: `creator_reward_${purchaseData.environmentId}`
                })
            });

            let rewardResult = null;
            if (rewardResponse.ok) {
                rewardResult = await rewardResponse.json();
                console.log(`🎁 Creator reward distribution initiated:`, rewardResult);
            } else {
                const errorText = await rewardResponse.text();
                console.error(`Creator reward distribution failed: ${rewardResponse.status} - ${errorText}`);
            }

            return {
                success: true,
                transactionHash: tx.hash,
                blockNumber: receipt.blockNumber,
                creatorReward: rewardResult
            };

        } catch (error) {
            console.error('Error making real environment purchase:', error);
            console.error('Error details:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Simulate REAL attack with ACTUAL transactions
     */
    async function simulateRealAttack(attacker, config) {
        const results = {
            successfulEarnings: 0,
            blockedAttempts: 0,
            totalEthEarned: 0
        };

        console.log(`🔥 Starting ${config.attackType} attack from ${attacker.address.slice(-8)}`);

        for (let i = 0; i < config.attempts; i++) {
            try {
                if (config.attackType === 'rapid_earning_withdrawal') {
                    // Try to earn maximum tokens rapidly through YOUR server
                    const earningResult = await makeGameplayEarning(attacker, {
                        amount: 0.01, // Try to earn 0.01 ETH rapidly
                        reason: `rapid_earning_attempt_${i + 1}`
                    });

                    if (earningResult.success) {
                        results.successfulEarnings++;
                        results.totalEthEarned += 0.01;

                        // Immediately try to withdraw through YOUR server
                        const withdrawResult = await requestRealWithdrawal(attacker, 0.01);
                        if (!withdrawResult.success) {
                            results.blockedAttempts++;
                            console.log(`🛡️ Withdrawal blocked for ${attacker.address.slice(-8)}`);
                        }
                    } else {
                        results.blockedAttempts++;
                        console.log(`🛡️ Earning blocked for ${attacker.address.slice(-8)}`);
                    }
                }

                // Small delay between attack attempts
                await new Promise(resolve => setTimeout(resolve, 50));

            } catch (error) {
                results.blockedAttempts++;
                console.log(`🛡️ Attack attempt blocked: ${error.message}`);
            }
        }

        console.log(`📊 Attack completed for ${attacker.address.slice(-8)}: ${results.successfulEarnings} successful, ${results.blockedAttempts} blocked`);
        return results;
    }

    /**
     * Request REAL withdrawal from YOUR hot wallet
     */
    async function requestRealWithdrawal(user, amount) {
        try {
            const response = await fetch(`${gameServerUrl}/api/wallet/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer secure-token-for-development'
                },
                body: JSON.stringify({
                    toAddress: user.address,
                    amount: amount.toString(),
                    reason: `withdrawal_${user.address.slice(-8)}`
                })
            });

            if (!response.ok) {
                return { success: false, error: `Withdrawal failed: ${response.status}` };
            }

            const result = await response.json();
            console.log(`💸 Withdrawal successful: ${amount} ETH to ${user.address.slice(-8)}`);
            return { success: true, ...result };

        } catch (error) {
            return { success: false, error: error.message };
        }
    }
});
