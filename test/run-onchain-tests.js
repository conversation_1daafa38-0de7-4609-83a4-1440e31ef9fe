#!/usr/bin/env node

/**
 * ON-CHAIN Test Runner
 * 
 * This script runs the on-chain treasury drain protection tests.
 * 
 * Prerequisites:
 * 1. Start your Hardhat local node: npx hardhat node
 * 2. Start your game server: node server/index.js
 * 3. Run this script: node test/run-onchain-tests.js
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fetch from 'node-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🚀 ON-CHAIN Treasury Drain Protection Test Runner');
console.log('================================================');

async function checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check if Hardhat node is running
    try {
        const response = await fetch('http://localhost:8545', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'eth_blockNumber',
                params: [],
                id: 1
            })
        });
        
        if (response.ok) {
            console.log('✅ Hardhat local node is running on localhost:8545');
        } else {
            throw new Error('Hardhat node not responding');
        }
    } catch (error) {
        console.error('❌ Hardhat local node is NOT running!');
        console.error('   Please start it with: npx hardhat node');
        process.exit(1);
    }
    
    // Check if game server is running
    try {
        const response = await fetch('http://localhost:3001/api/health');
        if (response.ok) {
            console.log('✅ Game server is running on localhost:3001');
        } else {
            throw new Error('Game server not responding');
        }
    } catch (error) {
        console.error('❌ Game server is NOT running!');
        console.error('   Please start it with: node server/index.js');
        process.exit(1);
    }
    
    console.log('✅ All prerequisites met!');
    console.log('');
}

function runTests() {
    console.log('🧪 Running ON-CHAIN Treasury Drain Protection Tests...');
    console.log('');
    
    const testProcess = spawn('node', [
        '--experimental-vm-modules',
        '--no-warnings',
        'node_modules/jest/bin/jest.js',
        'test/tokenomics/OnChainTreasury.test.js',
        '--verbose',
        '--no-cache',
        '--testEnvironment=node'
    ], {
        cwd: projectRoot,
        stdio: 'inherit'
    });
    
    testProcess.on('close', (code) => {
        console.log('');
        if (code === 0) {
            console.log('✅ All tests passed! Treasury drain protection is working.');
        } else {
            console.log('❌ Some tests failed. Check the output above for details.');
        }
        process.exit(code);
    });
    
    testProcess.on('error', (error) => {
        console.error('❌ Failed to run tests:', error.message);
        process.exit(1);
    });
}

async function main() {
    try {
        await checkPrerequisites();
        runTests();
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

main();
