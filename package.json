{"name": "ChaosCruiser-game", "version": "1.0.0", "description": "A vertical scrolling shooter with reality-warping mechanics", "type": "module", "scripts": {"start": "node start.js", "dev": "vite", "build": "vite build", "preview": "vite preview", "test": "node --experimental-vm-modules --no-warnings node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules --no-warnings node_modules/jest/bin/jest.js --watch", "server": "cd server && npm start", "server:dev": "cd server && npm run dev", "dev:all": "concurrently \"npm run server:dev\" \"npm run dev\"", "network": "node start.js network", "frontend:network": "node start.js frontend:network"}, "devDependencies": {"concurrently": "^8.2.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "nodemon": "^3.0.2", "vite": "^5.0.0"}, "dependencies": {"dotenv": "^17.2.1", "ethers": "^6.15.0", "gg-game-sdk": "^1.3.0", "jsdom": "^27.0.0", "node-fetch": "^3.3.2"}}