{"environments": [{"id": "env_1_1756654556342", "creatorUserId": "user_1756654556315_5s6n73ksu", "createdAt": 1756654556342, "imageFileName": "env_1756654556315.jpg", "name": "Custom Environment", "description": "Custom Environment", "type": "custom", "imagePrompt": "Generate a 2048x512 pixel image of a dark, neon-lit background with glowing super saiyan shadow demons in mid-air, surrounded by wisps of electricity; demons are translucent with a dark blue aura; the background is a gradient of deep purples and blacks, with sparks of bright blue electricity. The image is set in a futuristic, otherworldly environment with a sense of speed and urgency.", "gameplayModifiers": {"enemySpeedMultiplier": 1.2, "enemyHealthMultiplier": 0.8, "enemySpawnRateMultiplier": 1.5, "enemyProjectileSpeedMultiplier": 1, "environmentEffects": ["Enemies take 20% increased damage from laser blasts.", "Enemies are 30% more likely to spawn in clusters, increasing difficulty."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "electrical_storms", "damagePerSecond": 5, "slowEffect": 0.2}], "enemyTypeModifiers": {"water": 1, "fire": 1, "air": 1, "earth": 1, "crystal": 1.2, "shadow": 0.8}}, "imageUrl": "http://localhost:3001/api/images/1756654556299-glowing-super-saiyan-shadow-demons.jpg", "imageData": {"images": [{"url": "/api/images/1756654556299-glowing-super-saiyan-shadow-demons.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1756654556299-glowing-super-saiyan-shadow-demons.jpg", "localUrl": "http://localhost:3001/api/images/1756654556299-glowing-super-saiyan-shadow-demons.jpg"}], "timings": {"inference": 4.28837322909385}, "seed": 13068782599194472000, "has_nsfw_concepts": [false], "prompt": "Generate a 2048x512 pixel image of a dark, neon-lit background with glowing super saiyan shadow demons in mid-air, surrounded by wisps of electricity; demons are translucent with a dark blue aura; the background is a gradient of deep purples and blacks, with sparks of bright blue electricity. The image is set in a futuristic, otherworldly environment with a sense of speed and urgency."}, "timesUsed": 0, "totalPurchases": 3, "totalRevenue": 30000, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 4}, {"id": "env_2_1756743681799", "creatorUserId": "user_1756530070709_p9rw2v3mc", "createdAt": 1756743681799, "imageFileName": "env_1756743681751.jpg", "name": "Goblin World", "description": "A dark and twisted realm inhabited by malevolent green goblins.", "type": "custom", "imagePrompt": "Generate a dark, eerie background with twisted, nightmarish green goblin architecture in the style of a cyberpunk world. Include flickering neon lights, dimly lit caverns, and an ominous, foreboding atmosphere. The image should be high-contrast with deep shadows and a sense of unease. Include random goblin silhouettes and eerie, glowing orbs to create a sense of unease. The background should have a sense of depth and dimensionality, with layers of twisted metal and concrete.", "gameplayModifiers": {"enemySpeedMultiplier": 1.2, "enemyHealthMultiplier": 1.1, "enemySpawnRateMultiplier": 1.1, "enemyProjectileSpeedMultiplier": 1, "environmentEffects": ["The green goblin environment amplifies the strength of fire attacks against enemies."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal"], "environmentHazards": [{"type": "lava_pools", "damagePerSecond": 2, "slowEffect": 0.2}, {"type": "electrical_storms", "damagePerSecond": 3, "slowEffect": 0.1}], "enemyTypeModifiers": {"water": 1.1, "fire": 1.2, "air": 1, "earth": 0.9, "crystal": 1}}, "imageUrl": "http://localhost:3001/api/images/1756743681725-scary-green-goblin-world.jpg", "imageData": {"images": [{"url": "/api/images/1756743681725-scary-green-goblin-world.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1756743681725-scary-green-goblin-world.jpg", "localUrl": "http://localhost:3001/api/images/1756743681725-scary-green-goblin-world.jpg"}], "timings": {"inference": 4.203110612987075}, "seed": 11640413764240538000, "has_nsfw_concepts": [false], "prompt": "Generate a dark, eerie background with twisted, nightmarish green goblin architecture in the style of a cyberpunk world. Include flickering neon lights, dimly lit caverns, and an ominous, foreboding atmosphere. The image should be high-contrast with deep shadows and a sense of unease. Include random goblin silhouettes and eerie, glowing orbs to create a sense of unease. The background should have a sense of depth and dimensionality, with layers of twisted metal and concrete."}, "timesUsed": 0, "totalPurchases": 16, "totalRevenue": 225000, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 4}, {"id": "env_3_1757189938067", "creatorUserId": "user_1757184063743_gb01gih2m", "createdAt": 1757189938068, "imageFileName": "env_1757189938049.jpg", "name": "Inferno Abyss", "description": "A scorching hot environment of lava and flames raining down.", "type": "custom", "imagePrompt": "A wide, dark orange and red background with scattered, large, molten lava rocks and pillars of flame reaching up to a dark, smoky sky. In the distance, a faint, fiery glow and a few, isolated, dark rock formations. The ground is covered with a thin layer of lava and there is a constant, heavy rain of lava, creating a sense of urgency and danger.", "gameplayModifiers": {"enemySpeedMultiplier": 1.2, "enemyHealthMultiplier": 1.5, "enemySpawnRateMultiplier": 1.8, "enemyProjectileSpeedMultiplier": 1.5, "environmentEffects": ["Enemies are more aggressive and have increased attack speed.", "Enemies spawn with increased health and deal more damage."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "lava_rain", "damagePerSecond": 3, "slowEffect": 0.2}, {"type": "molten_lava_pools", "damagePerSecond": 5, "slowEffect": 0.3}], "enemyTypeModifiers": {"water": 0.7, "fire": 1.2, "air": 1, "earth": 0.9, "crystal": 0.8, "shadow": 0.6}}, "imageUrl": "http://localhost:3001/api/images/1757189937311-flaming-lava-inferno-raining-lava.jpg", "imageData": {"images": [{"url": "/api/images/1757189937311-flaming-lava-inferno-raining-lava.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757189937311-flaming-lava-inferno-raining-lava.jpg", "localUrl": "http://localhost:3001/api/images/1757189937311-flaming-lava-inferno-raining-lava.jpg"}], "timings": {"inference": 4.217893138004001}, "seed": 16080143349901510000, "has_nsfw_concepts": [false], "prompt": "A wide, dark orange and red background with scattered, large, molten lava rocks and pillars of flame reaching up to a dark, smoky sky. In the distance, a faint, fiery glow and a few, isolated, dark rock formations. The ground is covered with a thin layer of lava and there is a constant, heavy rain of lava, creating a sense of urgency and danger."}, "timesUsed": 0, "totalPurchases": 4, "totalRevenue": 80000, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 5}, {"id": "env_4_1757192437295", "creatorUserId": "user_1757184063743_gb01gih2m", "createdAt": 1757192437295, "imageFileName": "env_1757192437268.jpg", "name": "Vineforest", "description": "Earthy forest world with dense foliage, vines, and rocky terrain", "type": "custom", "imagePrompt": "Generate a 16:9 background image with a dense, green forest environment. The image should feature tall trees with thick trunks and vines draped over them. The ground should be rocky with scattered boulders and ferns. The sky should be a cloudy blue with a hint of sun peeking through the trees. In the distance, a massive, twisted tree should rise above the others, its trunk glowing with a soft, ethereal light. The image should have a sense of mysticism and ancient power, with a hint of danger lurking in the shadows.", "gameplayModifiers": {"enemySpeedMultiplier": 0.9, "enemyHealthMultiplier": 1.1, "enemySpawnRateMultiplier": 1, "enemyProjectileSpeedMultiplier": 0.8, "environmentEffects": ["Enemy projectiles have a chance to get stuck in the environment, temporarily stunning the enemy.", "The environment provides cover for enemies, increasing their health in certain areas."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "vine_tangles", "damagePerSecond": 5, "slowEffect": 0.5}, {"type": "rockslides", "damagePerSecond": 1, "slowEffect": 0.2}], "enemyTypeModifiers": {"water": 1.2, "fire": 0.8, "air": 0.9, "earth": 1, "crystal": 0.7, "shadow": 1.3}}, "imageUrl": "http://localhost:3001/api/images/1757192437244-earthy-forest-world-with-vines-and-rocks.jpg", "imageData": {"images": [{"url": "/api/images/1757192437244-earthy-forest-world-with-vines-and-rocks.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757192437244-earthy-forest-world-with-vines-and-rocks.jpg", "localUrl": "http://localhost:3001/api/images/1757192437244-earthy-forest-world-with-vines-and-rocks.jpg"}], "timings": {"inference": 4.2067822279641405}, "seed": 9190799009445126000, "has_nsfw_concepts": [false], "prompt": "Generate a 16:9 background image with a dense, green forest environment. The image should feature tall trees with thick trunks and vines draped over them. The ground should be rocky with scattered boulders and ferns. The sky should be a cloudy blue with a hint of sun peeking through the trees. In the distance, a massive, twisted tree should rise above the others, its trunk glowing with a soft, ethereal light. The image should have a sense of mysticism and ancient power, with a hint of danger lurking in the shadows."}, "timesUsed": 0, "totalPurchases": 6, "totalRevenue": 60000, "lastUsed": null, "isActive": true, "tags": ["forest", "earth"], "difficulty": 3}, {"id": "env_5_1757202907914", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 1757202907914, "imageFileName": "env_1757202907876.jpg", "name": "Rocky Earth Crevasse", "description": "A deep, rugged crevasse carved into the surface of a rocky, barren planet.", "type": "custom", "imagePrompt": "A vertically-oriented rocky crevasse with jagged outcroppings, scattered boulders, and debris-filled ravines. The walls and floor are a mix of rust-red and dark grey stone, with hints of green-blue where minerals or water seep through. In the distance, the crevasse opens up to a vast, cavernous space with a hint of atmospheric haze. The sky above is a deep, foreboding grey, with hints of ash and smoke. Include sharp, defined shadows and highlights to emphasize the rocky texture.", "gameplayModifiers": {"enemySpeedMultiplier": 1, "enemyHealthMultiplier": 0.8, "enemySpawnRateMultiplier": 1.2, "enemyProjectileSpeedMultiplier": 0.9, "environmentEffects": ["Enemies spawn with increased frequency, but their projectiles and attacks deal slightly reduced damage.", "Enemies have a moderate chance to spawn with increased health, but reduced speed."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "lava_pools", "damagePerSecond": 5, "slowEffect": 0.2}], "enemyTypeModifiers": {"water": 0.8, "fire": 1.1, "air": 0.9, "earth": 1, "crystal": 1.1, "shadow": 0.8}}, "imageUrl": "http://localhost:3001/api/images/1757202907832-rocky-earth-crevasse.jpg", "imageData": {"images": [{"url": "/api/images/1757202907832-rocky-earth-crevasse.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757202907832-rocky-earth-crevasse.jpg", "localUrl": "http://localhost:3001/api/images/1757202907832-rocky-earth-crevasse.jpg"}], "timings": {"inference": 4.301033935975283}, "seed": 13656249326069795000, "has_nsfw_concepts": [false], "prompt": "A vertically-oriented rocky crevasse with jagged outcroppings, scattered boulders, and debris-filled ravines. The walls and floor are a mix of rust-red and dark grey stone, with hints of green-blue where minerals or water seep through. In the distance, the crevasse opens up to a vast, cavernous space with a hint of atmospheric haze. The sky above is a deep, foreboding grey, with hints of ash and smoke. Include sharp, defined shadows and highlights to emphasize the rocky texture."}, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 3}, {"id": "env_6_1757207693940", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 1757207693940, "imageFileName": "env_1757207693917.jpg", "name": "Apocalyptic Nightmare", "description": "A twisted, crystalline environment of shattered quartz shards.", "type": "custom", "imagePrompt": "A dark, surreal background of interlocking crystalline quartz shards, shattered and twisted into impossible angles. Shards of crystal refract and reflect flickering, eerie blue-green light from an unknown source. The shards seem to stretch and writhe like living tendrils, as if the very fabric of reality has been torn apart. In the distance, a faint, pulsating glow hints at a long-abandoned, ancient technology.", "gameplayModifiers": {"enemySpeedMultiplier": 1.5, "enemyHealthMultiplier": 1, "enemySpawnRateMultiplier": 0.75, "enemyProjectileSpeedMultiplier": 1.25, "environmentEffects": ["The crystalline shards create disorienting, flickering illusions that can blind players for a short duration.", "The twisted quartz shards can cause players to stumble or become temporarily vulnerable to enemy attacks."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "shattered_quartz_shards", "damagePerSecond": 5, "slowEffect": 0.25}, {"type": "distorted_refractions", "damagePerSecond": 3, "slowEffect": 0.15}], "enemyTypeModifiers": {"water": 1.25, "fire": 1, "air": 0.9, "earth": 1.2, "crystal": 0.75, "shadow": 1.75}}, "imageUrl": "http://localhost:3001/api/images/1757207693867-crystalline-quartz-shards-of-apocalyptic-nightmares.jpg", "imageData": {"images": [{"url": "/api/images/1757207693867-crystalline-quartz-shards-of-apocalyptic-nightmares.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757207693867-crystalline-quartz-shards-of-apocalyptic-nightmares.jpg", "localUrl": "http://localhost:3001/api/images/1757207693867-crystalline-quartz-shards-of-apocalyptic-nightmares.jpg"}], "timings": {"inference": 4.19533042400144}, "seed": 8783168570681897000, "has_nsfw_concepts": [false], "prompt": "A dark, surreal background of interlocking crystalline quartz shards, shattered and twisted into impossible angles. Shards of crystal refract and reflect flickering, eerie blue-green light from an unknown source. The shards seem to stretch and writhe like living tendrils, as if the very fabric of reality has been torn apart. In the distance, a faint, pulsating glow hints at a long-abandoned, ancient technology."}, "timesUsed": 0, "totalPurchases": 2, "totalRevenue": 30000, "lastUsed": null, "isActive": true, "tags": ["crystal"], "difficulty": 4}, {"id": "env_7_1757209196818", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 1757209196818, "imageFileName": "env_1757209196790.jpg", "name": "Nightmare Shards", "description": "A twisted, crystalline environment that assaults the senses with shards of nightmarish quartz psychosis.", "type": "custom", "imagePrompt": "Generate a surreal, vertical background with crystalline shards in shades of midnight blue and indigo. Shards should be of varying sizes, with some protruding from the ground like jagged teeth, while others hang suspended in mid-air like macabre wind chimes. The background should be a dark, starry night sky with an eerie, pulsing glow emanating from the shards. Incorporate subtle, abstract patterns and textures to give the image depth and visual interest.", "gameplayModifiers": {"enemySpeedMultiplier": 0.8, "enemyHealthMultiplier": 1.2, "enemySpawnRateMultiplier": 1.5, "enemyProjectileSpeedMultiplier": 0.8, "environmentEffects": ["Enemies within this environment will have a 20% increased chance to spawn a 'Crystal Fragment' projectile that deals moderate damage."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "crystal_shards", "damagePerSecond": 3, "slowEffect": 0.2}], "enemyTypeModifiers": {"water": 1, "fire": 0.8, "air": 0.9, "earth": 1.1, "crystal": 0.7, "shadow": 1.4}}, "imageUrl": "http://localhost:3001/api/images/1757209196729-crystalline-shards-of-nightmarish-quartz-psychosis.jpg", "imageData": {"images": [{"url": "/api/images/1757209196729-crystalline-shards-of-nightmarish-quartz-psychosis.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757209196729-crystalline-shards-of-nightmarish-quartz-psychosis.jpg", "localUrl": "http://localhost:3001/api/images/1757209196729-crystalline-shards-of-nightmarish-quartz-psychosis.jpg"}], "timings": {"inference": 4.2450161289889365}, "seed": 12347851310755580000, "has_nsfw_concepts": [false], "prompt": "Generate a surreal, vertical background with crystalline shards in shades of midnight blue and indigo. Shards should be of varying sizes, with some protruding from the ground like jagged teeth, while others hang suspended in mid-air like macabre wind chimes. The background should be a dark, starry night sky with an eerie, pulsing glow emanating from the shards. Incorporate subtle, abstract patterns and textures to give the image depth and visual interest."}, "timesUsed": 0, "totalPurchases": 4, "totalRevenue": 60000, "lastUsed": null, "isActive": true, "tags": ["crystal"], "difficulty": 4}, {"id": "env_8_1757209434541", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 1757209434541, "imageFileName": "env_1757209434518.jpg", "name": "Crystalline Apocalypse", "description": "A post-apocalyptic environment ravaged by crystalline quartz, with scattered asteroids and debris fields.", "type": "custom", "imagePrompt": "A dark, desolate landscape with a shattered crystalline quartz mountain range in the background, lit by the faint glow of distant stars. Scattered asteroids and debris fields can be seen throughout the environment, with occasional explosions and sparks illuminating the darkness. The sky is a deep shade of indigo, with a hazy texture to give it a sense of depth. In the foreground, a few scattered crystal formations can be seen, some of which are emitting a soft blue light. The overall atmosphere should be one of desolation and catastrophe.", "gameplayModifiers": {"enemySpeedMultiplier": 1.2, "enemyHealthMultiplier": 1, "enemySpawnRateMultiplier": 1.5, "enemyProjectileSpeedMultiplier": 1, "environmentEffects": ["Enemies spawn with increased aggression, but have reduced accuracy.", "The environment is littered with crystal formations that can be destroyed for bonus points."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "asteroid_field", "damagePerSecond": 5, "slowEffect": 0.2}, {"type": "crystal_shards", "damagePerSecond": 3, "slowEffect": 0.1}], "enemyTypeModifiers": {"water": {"speedMultiplier": 1.1, "healthMultiplier": 0.8}, "fire": {"speedMultiplier": 1.4, "healthMultiplier": 0.6}, "air": {"speedMultiplier": 1.1, "healthMultiplier": 0.9}, "earth": {"speedMultiplier": 1, "healthMultiplier": 1}, "crystal": {"speedMultiplier": 1.2, "healthMultiplier": 1.2}, "shadow": {"speedMultiplier": 1.5, "healthMultiplier": 0.7}}}, "imageUrl": "http://localhost:3001/api/images/1757209434478-crystalline-quartz-apocalypse.jpg", "imageData": {"images": [{"url": "/api/images/1757209434478-crystalline-quartz-apocalypse.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757209434478-crystalline-quartz-apocalypse.jpg", "localUrl": "http://localhost:3001/api/images/1757209434478-crystalline-quartz-apocalypse.jpg"}], "timings": {"inference": 4.2857956000370905}, "seed": 3060403663989347000, "has_nsfw_concepts": [false], "prompt": "A dark, desolate landscape with a shattered crystalline quartz mountain range in the background, lit by the faint glow of distant stars. Scattered asteroids and debris fields can be seen throughout the environment, with occasional explosions and sparks illuminating the darkness. The sky is a deep shade of indigo, with a hazy texture to give it a sense of depth. In the foreground, a few scattered crystal formations can be seen, some of which are emitting a soft blue light. The overall atmosphere should be one of desolation and catastrophe."}, "timesUsed": 0, "totalPurchases": 3, "totalRevenue": 45000, "lastUsed": null, "isActive": true, "tags": ["crystal"], "difficulty": 4}, {"id": "env_9_175**********", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 175**********, "imageFileName": "env_1757214547267.jpg", "name": "Rockforest", "description": "A post-apocalyptic environment with scattered boulders in forests, where trees are overgrown with rocky protrusions.", "type": "custom", "imagePrompt": "Create a detailed background image of a barren, rocky forest landscape with twisted, gnarled trees and massive boulders scattered about, in a muted color palette of earthy tones, with a distant, ominous sky.", "gameplayModifiers": {"enemySpeedMultiplier": 0.8, "enemyHealthMultiplier": 1.2, "enemySpawnRateMultiplier": 1.5, "enemyProjectileSpeedMultiplier": 1, "environmentEffects": ["Enemies will have a 10% chance to spawn with increased speed for 5 seconds after taking damage.", "The environment will occasionally trigger a moderate-sized boulder to fall, dealing 1 damage to the player per second for 5 seconds."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal"], "environmentHazards": [{"type": "boulder_fall", "damagePerSecond": 1, "slowEffect": 0}], "enemyTypeModifiers": {"water": 1, "fire": 1.2, "air": 0.9, "earth": 1, "crystal": 1.1}}, "imageUrl": "http://localhost:3001/api/images/1757214547218-apocalyptic-rocks-and-forests-with-boulders-growing-out-of-trees.jpg", "imageData": {"images": [{"url": "/api/images/1757214547218-apocalyptic-rocks-and-forests-with-boulders-growing-out-of-trees.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757214547218-apocalyptic-rocks-and-forests-with-boulders-growing-out-of-trees.jpg", "localUrl": "http://localhost:3001/api/images/1757214547218-apocalyptic-rocks-and-forests-with-boulders-growing-out-of-trees.jpg"}], "timings": {"inference": 4.242606175947003}, "seed": 2916830865458647600, "has_nsfw_concepts": [false], "prompt": "Create a detailed background image of a barren, rocky forest landscape with twisted, gnarled trees and massive boulders scattered about, in a muted color palette of earthy tones, with a distant, ominous sky."}, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "tags": ["forest"], "difficulty": 4}, {"id": "env_10_1757215134950", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 1757215134950, "imageFileName": "env_1757215134924.jpg", "name": "Dark Shadows", "description": "A shadowy conspiracy world, with dark shadows creeping toward the player.", "type": "custom", "imagePrompt": "Generate a dark, gritty, and eerie background image with dark shadows creeping towards the player from the background. Include a mix of dark blues, purples, and blacks to create a sense of foreboding. Add some flickering light sources in the distance to hint at the conspiracy. Include some twisted, abandoned structures in the background, with broken machinery and debris scattered everywhere. The player should be in the foreground, with a sense of urgency and danger. Use a mix of textures and shading to create depth and atmosphere. Include some subtle particle effects to enhance the sense of movement and energy.", "gameplayModifiers": {"enemySpeedMultiplier": 0.8, "enemyHealthMultiplier": 1.2, "enemySpawnRateMultiplier": 1.5, "enemyProjectileSpeedMultiplier": 1, "environmentEffects": ["Enemies gain increased health and speed in this environment.", "Enemies are more aggressive and prone to ambushes."], "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentHazards": [{"type": "shadows", "damagePerSecond": 2, "slowEffect": 0.2}], "enemyTypeModifiers": {"water": 1, "fire": 1.2, "air": 1, "earth": 1, "crystal": 0.8, "shadow": 1.5}}, "imageUrl": "http://localhost:3001/api/images/1757215134893-shadowy-conspiracy-world-with-dark-shadows-creeping-toward-me.jpg", "imageData": {"images": [{"url": "/api/images/1757215134893-shadowy-conspiracy-world-with-dark-shadows-creeping-toward-me.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757215134893-shadowy-conspiracy-world-with-dark-shadows-creeping-toward-me.jpg", "localUrl": "http://localhost:3001/api/images/1757215134893-shadowy-conspiracy-world-with-dark-shadows-creeping-toward-me.jpg"}], "timings": {"inference": 4.254860437009484}, "seed": 3228752124931662000, "has_nsfw_concepts": [false], "prompt": "Generate a dark, gritty, and eerie background image with dark shadows creeping towards the player from the background. Include a mix of dark blues, purples, and blacks to create a sense of foreboding. Add some flickering light sources in the distance to hint at the conspiracy. Include some twisted, abandoned structures in the background, with broken machinery and debris scattered everywhere. The player should be in the foreground, with a sense of urgency and danger. Use a mix of textures and shading to create depth and atmosphere. Include some subtle particle effects to enhance the sense of movement and energy."}, "timesUsed": 0, "totalPurchases": 1, "totalRevenue": 15000, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 4}, {"id": "env_11_1757216357061", "creatorUserId": "user_1757197795141_cpurql4aq", "createdAt": 1757216357061, "imageFileName": "env_1757216357014.jpg", "name": "<PERSON><PERSON>in <PERSON> Inferno", "description": "A chaotic environment filled with fiery red hot Flamin <PERSON>", "type": "custom", "imagePrompt": "Generate a vibrant and dynamic image of a firey fireball shower with Flamin <PERSON> scattered throughout the background, flames licking the edges of the screen, and a mesmerizing explosion of fire in the distance. The Flamin Cheetos are in different sizes and states, some are exploding, some are on fire, and some are intact. The colors are bright and neon, with shades of red, orange, and yellow dominating the palette. The background is a dark, gradient blue, representing space. Include some subtle sparks and embers to add depth and movement to the scene.", "gameplayModifiers": {"enemySpeedMultiplier": 1.2, "enemyHealthMultiplier": 0.8, "enemySpawnRateMultiplier": 1.5, "enemyProjectileSpeedMultiplier": 1, "environmentEffects": ["Fire from the Flamin Cheetos can set enemies on fire, dealing damage over time", "The intense heat from the environment causes enemies to move faster and more erratically"], "compatibleEnemyTypes": ["fire", "shadow"], "environmentHazards": [{"type": "flamin_cheetos", "damagePerSecond": 5, "slowEffect": 0.2}], "enemyTypeModifiers": {"water": 0.6, "fire": 1.5, "air": 0.8, "earth": 0.9, "crystal": 0.7, "shadow": 1.2}}, "imageUrl": "http://localhost:3001/api/images/1757216356992-firey-fireball-shower-with-firey-red-hot-flamin-cheetos.jpg", "imageData": {"images": [{"url": "/api/images/1757216356992-firey-fireball-shower-with-firey-red-hot-flamin-cheetos.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757216356992-firey-fireball-shower-with-firey-red-hot-flamin-cheetos.jpg", "localUrl": "http://localhost:3001/api/images/1757216356992-firey-fireball-shower-with-firey-red-hot-flamin-cheetos.jpg"}], "timings": {"inference": 4.266794618044514}, "seed": 829881607398429300, "has_nsfw_concepts": [false], "prompt": "Generate a vibrant and dynamic image of a firey fireball shower with Flamin <PERSON> scattered throughout the background, flames licking the edges of the screen, and a mesmerizing explosion of fire in the distance. The Flamin Cheetos are in different sizes and states, some are exploding, some are on fire, and some are intact. The colors are bright and neon, with shades of red, orange, and yellow dominating the palette. The background is a dark, gradient blue, representing space. Include some subtle sparks and embers to add depth and movement to the scene."}, "timesUsed": 0, "totalPurchases": 6, "totalRevenue": 90000, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 4}, {"id": "env_12_1757221439971", "creatorUserId": "user_1757218822708_d3295wvhg", "createdAt": 1757221439971, "imageFileName": "env_1757221439921.jpg", "name": "Conspiracy Depths", "description": "A shadowy world shrouded in darkness, where dimly lit figures lurk in every corner.", "type": "custom", "imagePrompt": "Surreal, dark, and gritty cityscape with skyscrapers and twisted alleyways. The sky above is a deep, foreboding gray, with flickering neon lights casting eerie shadows on the buildings. Dimly lit figures with glowing eyes lurk in the shadows, reaching out to grab the player. The atmosphere is tense and foreboding, with an undercurrent of malevolence.", "gameplayModifiers": {"compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentType": [{"type": "shadow"}], "environmentHazards": [{"type": "electrical_storms", "damagePerSecond": 5, "slowEffect": 0.3}], "enemyTypeModifiers": {"water": 0.8, "fire": 1.2, "air": 1, "earth": 0.9, "crystal": 0.7, "shadow": 1.5}}, "imageUrl": "http://localhost:3001/api/images/1757221439843-shadowy-conspiracy-world-with-dimly-lit-shadowy-figures-reaching-out-to-grab-me.jpg", "imageData": {"images": [{"url": "/api/images/1757221439843-shadowy-conspiracy-world-with-dimly-lit-shadowy-figures-reaching-out-to-grab-me.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757221439843-shadowy-conspiracy-world-with-dimly-lit-shadowy-figures-reaching-out-to-grab-me.jpg", "localUrl": "http://localhost:3001/api/images/1757221439843-shadowy-conspiracy-world-with-dimly-lit-shadowy-figures-reaching-out-to-grab-me.jpg"}], "timings": {"inference": 4.204210146970581}, "seed": 4212690078662720000, "has_nsfw_concepts": [false], "prompt": "Surreal, dark, and gritty cityscape with skyscrapers and twisted alleyways. The sky above is a deep, foreboding gray, with flickering neon lights casting eerie shadows on the buildings. Dimly lit figures with glowing eyes lurk in the shadows, reaching out to grab the player. The atmosphere is tense and foreboding, with an undercurrent of malevolence."}, "timesUsed": 0, "totalPurchases": 1, "totalRevenue": 10000, "lastUsed": null, "isActive": true, "tags": [], "difficulty": 3}, {"id": "env_13_1757270830054", "creatorUserId": "user_1757270829999_tf1ymckmj", "createdAt": 1757270830054, "imageFileName": "env_1757270829999.jpg", "name": "Aquatic Abomination", "description": "A bubbly, watery ocean world filled with monstrous whale-like sea monsters and white water rapids.", "type": "custom", "imagePrompt": "Generate a futuristic, high-resolution image of a massive, underwater cityscape with sprawling metropolises and towering structures, partially submerged in a tumultuous ocean. The water is a deep shade of blue, with hints of turquoise and aquamarine, and features powerful white water rapids that cascade downwards from the city's edge. The sea floor is covered in a thick layer of seaweed, and whale-like sea monsters can be seen breaching the surface of the water, with schools of smaller fish darting about their giant forms. The city's architecture is a mix of organic and synthetic materials, with neon lights illuminating the darkened depths of the ocean. The image should be centered around a massive, twisted metal structure that pierces the surface of the water, with a swirling vortex of water and debris surrounding it. The color palette should be a mix of deep blues, greens, and purples, with neon accents of pink, blue, and yellow.", "gameplayModifiers": {"compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentType": [{"type": "water"}], "environmentHazards": [{"type": "electrical_storms", "damagePerSecond": 5, "slowEffect": 0.5}, {"type": "whirlpool", "damagePerSecond": 2, "slowEffect": 0.8}], "enemyTypeModifiers": {"water": 1, "fire": 0.8, "air": 0.7, "earth": 0.9, "crystal": 1.2, "shadow": 0.5}}, "imageUrl": "http://localhost:3001/api/images/1757270829955-bubbling-watery-ocean-world-with-white-water-rapids-and-monstrous-whale-like-sea-monsters.jpg", "imageData": {"images": [{"url": "/api/images/1757270829955-bubbling-watery-ocean-world-with-white-water-rapids-and-monstrous-whale-like-sea-monsters.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1757270829955-bubbling-watery-ocean-world-with-white-water-rapids-and-monstrous-whale-like-sea-monsters.jpg", "localUrl": "http://localhost:3001/api/images/1757270829955-bubbling-watery-ocean-world-with-white-water-rapids-and-monstrous-whale-like-sea-monsters.jpg"}], "timings": {"inference": 4.317607725970447}, "seed": 10134714744826900000, "has_nsfw_concepts": [false], "prompt": "Generate a futuristic, high-resolution image of a massive, underwater cityscape with sprawling metropolises and towering structures, partially submerged in a tumultuous ocean. The water is a deep shade of blue, with hints of turquoise and aquamarine, and features powerful white water rapids that cascade downwards from the city's edge. The sea floor is covered in a thick layer of seaweed, and whale-like sea monsters can be seen breaching the surface of the water, with schools of smaller fish darting about their giant forms. The city's architecture is a mix of organic and synthetic materials, with neon lights illuminating the darkened depths of the ocean. The image should be centered around a massive, twisted metal structure that pierces the surface of the water, with a swirling vortex of water and debris surrounding it. The color palette should be a mix of deep blues, greens, and purples, with neon accents of pink, blue, and yellow."}, "timesUsed": 0, "totalPurchases": 1, "totalRevenue": 10000, "lastUsed": null, "isActive": true, "environmentType": "water", "difficulty": 3}, {"id": "env_14_1758160857799", "creatorUserId": "user_1758160857702_yqejo9urx", "createdAt": 1758160857799, "imageFileName": "env_1758160857710.jpg", "name": "Egg Dystopia", "description": "A world overrun by shadowy fried egg people", "type": "custom", "imagePrompt": "generate a surreal and dark background featuring a ruined cityscape composed of oversized fried eggs, with shadowy figures of fried egg people scurrying around, set against a deep red and orange sky with a thick atmosphere of smoke and fog, with a few glowing purple eggs in the distance, with a sense of foreboding and desolation", "gameplayModifiers": {"compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentType": [{"type": "shadow"}], "environmentHazards": [{"type": "lava_pools", "damagePerSecond": 5, "slowEffect": 0.8}], "enemyTypeModifiers": {"water": 0.8, "fire": 1.2, "air": 0.8, "earth": 0.8, "crystal": 0.6, "shadow": 1.5}}, "imageUrl": "http://localhost:3001/api/images/1758160857605-incredible-edible-egg-dystopia-with-shadowy-fried-egg-people-everywhere.jpg", "imageData": {"images": [{"url": "/api/images/1758160857605-incredible-edible-egg-dystopia-with-shadowy-fried-egg-people-everywhere.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1758160857605-incredible-edible-egg-dystopia-with-shadowy-fried-egg-people-everywhere.jpg", "localUrl": "http://localhost:3001/api/images/1758160857605-incredible-edible-egg-dystopia-with-shadowy-fried-egg-people-everywhere.jpg"}], "timings": {"inference": 4.1864055269397795}, "seed": 6511735357738817000, "has_nsfw_concepts": [false], "prompt": "generate a surreal and dark background featuring a ruined cityscape composed of oversized fried eggs, with shadowy figures of fried egg people scurrying around, set against a deep red and orange sky with a thick atmosphere of smoke and fog, with a few glowing purple eggs in the distance, with a sense of foreboding and desolation"}, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "shadow", "difficulty": 3}, {"id": "env_15_1758321217868", "creatorUserId": "user_1758319912875_elupxulyj", "createdAt": 1758321217868, "imageFileName": "env_1758321217782.jpg", "name": "Glowing Vortex", "description": "A swirling, glowing firenado that rages through space.", "type": "custom", "imagePrompt": "A surreal, psychedelic background image of a glowing super saiyan firenado with vibrant orange and yellow hues swirling through space, surrounded by distant stars and planets, with intense heat distortion and turbulence effects visible in the air.", "gameplayModifiers": {"compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentType": [{"type": "fire"}], "environmentHazards": [{"type": "lava_pools", "damagePerSecond": 5, "slowEffect": 0.5}, {"type": "electrical_storms", "damagePerSecond": 2, "slowEffect": 0.7}], "enemyTypeModifiers": {"water": 1.2, "fire": 1.8, "air": 0.8, "earth": 0.9, "crystal": 1.5, "shadow": 1}}, "imageUrl": "http://localhost:3001/api/images/1758321217746-glowing-super-saiyan-firenado.jpg", "imageData": {"images": [{"url": "/api/images/1758321217746-glowing-super-saiyan-firenado.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1758321217746-glowing-super-saiyan-firenado.jpg", "localUrl": "http://localhost:3001/api/images/1758321217746-glowing-super-saiyan-firenado.jpg"}], "timings": {"inference": 4.460060162935406}, "seed": 5911887887524448000, "has_nsfw_concepts": [false], "prompt": "A surreal, psychedelic background image of a glowing super saiyan firenado with vibrant orange and yellow hues swirling through space, surrounded by distant stars and planets, with intense heat distortion and turbulence effects visible in the air."}, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "fire", "difficulty": 3}, {"id": "env_16_1758322706969", "creatorUserId": "user_1758322706921_cjxra1bl3", "createdAt": 1758322706969, "imageFileName": "env_1758322706921.jpg", "name": "Shadowing Abyss", "description": "A dark and treacherous crystalline environment where shadows reign supreme", "type": "custom", "imagePrompt": "Generate a dark, eerie, and foreboding image of a vast, underground cavern filled with glittering crystalline structures that reflect dim, flickering shadows. The walls and ceiling are covered in a thick layer of dark, glassy crystals that refract and distort the light, creating an otherworldly ambiance. In the distance, a series of glowing, ethereal orbs hover, casting an eerie blue glow over the scene. The ground is littered with jagged crystals and dark, twisted rock formations. The overall mood is one of impending doom and danger.", "gameplayModifiers": {"compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"], "environmentType": [{"type": "shadow"}], "environmentHazards": [{"type": "ice_patches", "damagePerSecond": 2, "slowEffect": 0.7}, {"type": "electrical_storms", "damagePerSecond": 5, "slowEffect": 0}], "enemyTypeModifiers": {"water": 1.2, "fire": 0.8, "air": 1, "earth": 0.9, "crystal": 1.5, "shadow": 0.6}}, "imageUrl": "http://localhost:3001/api/images/1758322706815-crystalline-shadow-nightmare.jpg", "imageData": {"images": [{"url": "/api/images/1758322706815-crystalline-shadow-nightmare.jpg", "width": 768, "height": 1360, "content_type": "image/jpeg", "localPath": "/home/<USER>/Downloads/AIGames/OrangeDefense/server/images/1758322706815-crystalline-shadow-nightmare.jpg", "localUrl": "http://localhost:3001/api/images/1758322706815-crystalline-shadow-nightmare.jpg"}], "timings": {"inference": 4.211654460988939}, "seed": 11715290904805085000, "has_nsfw_concepts": [false], "prompt": "Generate a dark, eerie, and foreboding image of a vast, underground cavern filled with glittering crystalline structures that reflect dim, flickering shadows. The walls and ceiling are covered in a thick layer of dark, glassy crystals that refract and distort the light, creating an otherworldly ambiance. In the distance, a series of glowing, ethereal orbs hover, casting an eerie blue glow over the scene. The ground is littered with jagged crystals and dark, twisted rock formations. The overall mood is one of impending doom and danger."}, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "shadow", "difficulty": 3}, {"id": "env_17_1758671188500", "creatorUserId": "******************************************", "createdAt": 1758671188500, "imageFileName": null, "name": "Mystical Test Environment 1758671188496", "description": "A mystical environment for testing creator rewards", "type": "space", "imagePrompt": "", "gameplayModifiers": {"environmentType": "mystical", "difficulty": "normal", "specialEffects": ["creator_reward_eligible"]}, "imageUrl": null, "imageData": null, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "space", "difficulty": 3}, {"id": "env_18_1758671226747", "creatorUserId": "******************************************", "createdAt": 1758671226747, "imageFileName": null, "name": "Test Environment", "description": "Test", "type": "space", "imagePrompt": "", "gameplayModifiers": {"environmentType": "mystical"}, "imageUrl": null, "imageData": null, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "space", "difficulty": 3}, {"id": "env_19_1758671288039", "creatorUserId": "******************************************", "createdAt": 1758671288039, "imageFileName": null, "name": "Mystical Test Environment 1758671288035", "description": "A mystical environment for testing creator rewards", "type": "space", "imagePrompt": "", "gameplayModifiers": {"environmentType": "mystical", "difficulty": "normal", "specialEffects": ["creator_reward_eligible"]}, "imageUrl": null, "imageData": null, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "space", "difficulty": 3}, {"id": "env_20_1758671432161", "creatorUserId": "******************************************", "createdAt": 1758671432161, "imageFileName": null, "name": "Mystical Test Environment 1758671432154", "description": "A mystical environment for testing creator rewards", "type": "space", "imagePrompt": "", "gameplayModifiers": {"environmentType": "mystical", "difficulty": "normal", "specialEffects": ["creator_reward_eligible"]}, "imageUrl": null, "imageData": null, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "space", "difficulty": 3}, {"id": "env_21_1758671500364", "creatorUserId": "******************************************", "createdAt": 1758671500364, "imageFileName": null, "name": "Mystical Test Environment 1758671500343", "description": "A mystical environment for testing creator rewards", "type": "space", "imagePrompt": "", "gameplayModifiers": {"environmentType": "mystical", "difficulty": "normal", "specialEffects": ["creator_reward_eligible"]}, "imageUrl": null, "imageData": null, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "space", "difficulty": 3}, {"id": "env_22_1758671730230", "creatorUserId": "******************************************", "createdAt": 1758671730230, "imageFileName": null, "name": "Debug Test Environment 1758671730140", "description": "A test environment for debugging", "type": "space", "imagePrompt": "", "gameplayModifiers": {"environmentType": "mystical", "difficulty": "normal"}, "imageUrl": null, "imageData": null, "timesUsed": 0, "totalPurchases": 0, "totalRevenue": 0, "lastUsed": null, "isActive": true, "environmentType": "space", "difficulty": 3}], "userEnvironments": {"user_1756654556315_5s6n73ksu": ["env_1_1756654556342"], "user_1756530070709_p9rw2v3mc": ["env_2_1756743681799"], "user_1757184063743_gb01gih2m": ["env_3_1757189938067", "env_4_1757192437295"], "user_1757197795141_cpurql4aq": ["env_5_1757202907914", "env_6_1757207693940", "env_7_1757209196818", "env_8_1757209434541", "env_9_175**********", "env_10_1757215134950", "env_11_1757216357061"], "user_1757218822708_d3295wvhg": ["env_12_1757221439971"], "user_1757270829999_tf1ymckmj": ["env_13_1757270830054"], "user_1758160857702_yqejo9urx": ["env_14_1758160857799"], "user_1758319912875_elupxulyj": ["env_15_1758321217868"], "user_1758322706921_cjxra1bl3": ["env_16_1758322706969"], "******************************************": ["env_17_1758671188500", "env_18_1758671226747", "env_19_1758671288039", "env_20_1758671432161", "env_21_1758671500364", "env_22_1758671730230"]}, "environmentStats": {"env_1_1756654556342": {"views": 0, "purchases": 3, "revenue": 30000, "ratings": [], "averageRating": 0}, "env_2_1756743681799": {"views": 0, "purchases": 16, "revenue": 225000, "ratings": [], "averageRating": 0}, "env_3_1757189938067": {"views": 0, "purchases": 4, "revenue": 80000, "ratings": [], "averageRating": 0}, "env_4_1757192437295": {"views": 0, "purchases": 6, "revenue": 60000, "ratings": [], "averageRating": 0}, "env_5_1757202907914": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_6_1757207693940": {"views": 0, "purchases": 2, "revenue": 30000, "ratings": [], "averageRating": 0}, "env_7_1757209196818": {"views": 0, "purchases": 4, "revenue": 60000, "ratings": [], "averageRating": 0}, "env_8_1757209434541": {"views": 0, "purchases": 3, "revenue": 45000, "ratings": [], "averageRating": 0}, "env_9_175**********": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_10_1757215134950": {"views": 0, "purchases": 1, "revenue": 15000, "ratings": [], "averageRating": 0}, "env_11_1757216357061": {"views": 0, "purchases": 6, "revenue": 90000, "ratings": [], "averageRating": 0}, "env_12_1757221439971": {"views": 0, "purchases": 1, "revenue": 10000, "ratings": [], "averageRating": 0}, "env_13_1757270830054": {"views": 0, "purchases": 1, "revenue": 10000, "ratings": [], "averageRating": 0}, "env_14_1758160857799": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_15_1758321217868": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_16_1758322706969": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_17_1758671188500": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_18_1758671226747": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_19_1758671288039": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_20_1758671432161": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_21_1758671500364": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}, "env_22_1758671730230": {"views": 0, "purchases": 0, "revenue": 0, "ratings": [], "averageRating": 0}}, "nextEnvironmentId": 23}